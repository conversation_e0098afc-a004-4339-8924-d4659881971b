const axios = require('axios');
const config = require('../config/setup');
const { User } = require('../database');
const userCache = require('../database/cache/models/user');

/**
 * Spotify Web API utility functions
 */
class SpotifyAPI {
    constructor() {
        this.clientId = config.apis.spotify.clientId;
        this.clientSecret = config.apis.spotify.clientSecret;
        this.redirectUri = config.apis.spotify.redirectUri;
        this.baseURL = 'https://api.spotify.com/v1';
        this.authURL = 'https://accounts.spotify.com';
    }

    /**
     * Generate authorization URL for OAuth2 flow
     * @param {string} state - State parameter for security
     * @returns {string} Authorization URL
     */
    getAuthURL(state) {
        const scopes = [
            'user-read-private',
            'user-read-email',
            'user-read-playback-state',
            'user-modify-playback-state',
            'user-read-currently-playing',
            'user-library-read',
            'user-library-modify',
            'user-top-read',
            'playlist-read-private',
            'playlist-modify-public',
            'playlist-modify-private'
        ].join(' ');

        const params = new URLSearchParams({
            response_type: 'code',
            client_id: this.clientId,
            scope: scopes,
            redirect_uri: this.redirectUri,
            state: state
        });

        return `${this.authURL}/authorize?${params.toString()}`;
    }

    /**
     * Exchange authorization code for access token
     * @param {string} code - Authorization code
     * @returns {Object} Token data
     */
    async exchangeCodeForToken(code) {
        try {
            const response = await axios.post(`${this.authURL}/api/token`, {
                grant_type: 'authorization_code',
                code: code,
                redirect_uri: this.redirectUri,
                client_id: this.clientId,
                client_secret: this.clientSecret
            }, {
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded'
                }
            });

            return response.data;
        } catch (error) {
            throw new Error(`Failed to exchange code for token: ${error.response?.data?.error_description || error.message}`);
        }
    }

    /**
     * Refresh access token using refresh token
     * @param {string} refreshToken - Refresh token
     * @returns {Object} New token data
     */
    async refreshAccessToken(refreshToken) {
        try {
            const response = await axios.post(`${this.authURL}/api/token`, {
                grant_type: 'refresh_token',
                refresh_token: refreshToken,
                client_id: this.clientId,
                client_secret: this.clientSecret
            }, {
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded'
                }
            });

            return response.data;
        } catch (error) {
            throw new Error(`Failed to refresh token: ${error.response?.data?.error_description || error.message}`);
        }
    }

    /**
     * Get valid access token for user (refresh if needed)
     * @param {string} userId - Discord user ID
     * @returns {string|null} Valid access token or null if not authenticated
     */
    async getValidToken(userId) {
        try {
            const userData = await userCache.getUser(userId);
            if (!userData?.Spotify?.accessToken) {
                return null;
            }

            // Check if token is expired (with 5 minute buffer)
            const now = new Date();
            const expiry = new Date(userData.Spotify.tokenExpiry);
            const bufferTime = 5 * 60 * 1000; // 5 minutes in milliseconds

            if (now.getTime() >= (expiry.getTime() - bufferTime)) {
                // Token is expired or about to expire, refresh it
                if (!userData.Spotify.refreshToken) {
                    return null;
                }

                const tokenData = await this.refreshAccessToken(userData.Spotify.refreshToken);
                
                // Update user data with new token
                const newExpiry = new Date(Date.now() + (tokenData.expires_in * 1000));
                await User.updateOne(
                    { UserID: userId },
                    {
                        $set: {
                            'Spotify.accessToken': tokenData.access_token,
                            'Spotify.tokenExpiry': newExpiry,
                            ...(tokenData.refresh_token && { 'Spotify.refreshToken': tokenData.refresh_token })
                        }
                    }
                );

                // Update cache
                await userCache.updateUser(userId, {
                    'Spotify.accessToken': tokenData.access_token,
                    'Spotify.tokenExpiry': newExpiry,
                    ...(tokenData.refresh_token && { 'Spotify.refreshToken': tokenData.refresh_token })
                });

                return tokenData.access_token;
            }

            return userData.Spotify.accessToken;
        } catch (error) {
            console.error('Error getting valid token:', error);
            return null;
        }
    }

    /**
     * Make authenticated request to Spotify API
     * @param {string} userId - Discord user ID
     * @param {string} endpoint - API endpoint
     * @param {string} method - HTTP method
     * @param {Object} data - Request data
     * @returns {Object} API response
     */
    async makeRequest(userId, endpoint, method = 'GET', data = null) {
        const token = await this.getValidToken(userId);
        if (!token) {
            throw new Error('User not authenticated with Spotify');
        }

        try {
            const config = {
                method: method,
                url: `${this.baseURL}${endpoint}`,
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            };

            if (data && (method === 'POST' || method === 'PUT' || method === 'PATCH')) {
                config.data = data;
            }

            const response = await axios(config);
            return response.data;
        } catch (error) {
            if (error.response?.status === 401) {
                // Token is invalid, user needs to re-authenticate
                throw new Error('Spotify authentication expired. Please run `spotify login` again.');
            }
            throw new Error(`Spotify API error: ${error.response?.data?.error?.message || error.message}`);
        }
    }

    /**
     * Get user's Spotify profile
     * @param {string} userId - Discord user ID
     * @returns {Object} User profile data
     */
    async getUserProfile(userId) {
        return await this.makeRequest(userId, '/me');
    }

    /**
     * Get user's current playback state
     * @param {string} userId - Discord user ID
     * @returns {Object|null} Playback state or null if nothing playing
     */
    async getCurrentPlayback(userId) {
        try {
            return await this.makeRequest(userId, '/me/player');
        } catch (error) {
            if (error.message.includes('No active device')) {
                return null;
            }
            throw error;
        }
    }

    /**
     * Get user's available devices
     * @param {string} userId - Discord user ID
     * @returns {Array} List of available devices
     */
    async getDevices(userId) {
        const response = await this.makeRequest(userId, '/me/player/devices');
        return response.devices || [];
    }

    /**
     * Search for tracks, artists, albums, or playlists
     * @param {string} userId - Discord user ID
     * @param {string} query - Search query
     * @param {string} type - Search type (track, artist, album, playlist)
     * @param {number} limit - Number of results to return
     * @returns {Object} Search results
     */
    async search(userId, query, type = 'track', limit = 20) {
        const endpoint = `/search?q=${encodeURIComponent(query)}&type=${type}&limit=${limit}`;
        return await this.makeRequest(userId, endpoint);
    }

    /**
     * Get user's top tracks or artists
     * @param {string} userId - Discord user ID
     * @param {string} type - 'tracks' or 'artists'
     * @param {string} timeRange - 'short_term', 'medium_term', or 'long_term'
     * @param {number} limit - Number of results to return
     * @returns {Object} Top items
     */
    async getTopItems(userId, type, timeRange = 'medium_term', limit = 20) {
        const endpoint = `/me/top/${type}?time_range=${timeRange}&limit=${limit}`;
        return await this.makeRequest(userId, endpoint);
    }

    /**
     * Format duration from milliseconds to MM:SS
     * @param {number} ms - Duration in milliseconds
     * @returns {string} Formatted duration
     */
    formatDuration(ms) {
        const minutes = Math.floor(ms / 60000);
        const seconds = Math.floor((ms % 60000) / 1000);
        return `${minutes}:${seconds.toString().padStart(2, '0')}`;
    }

    /**
     * Format time range for display
     * @param {string} timeRange - Spotify time range
     * @returns {string} Human readable time range
     */
    formatTimeRange(timeRange) {
        const ranges = {
            'short_term': 'Last 4 weeks',
            'medium_term': 'Last 6 months',
            'long_term': 'All time'
        };
        return ranges[timeRange] || timeRange;
    }
}

module.exports = new SpotifyAPI();
