const { embeds } = require('../../core/embeds');
const { ensureUserData } = require('../../database');
const userCache = require('../../database/cache/models/user');
const axios = require('axios');

module.exports = {
    name: 'interactionCreate',
    async execute(interaction, client) {
        if (!interaction.isModalSubmit()) return;

        // Handle Spotify token modal
        if (interaction.customId.startsWith('spotify_modal_')) {
            const userId = interaction.customId.split('_')[2];
            
            if (interaction.user.id !== userId) {
                return interaction.reply({
                    content: '❌ This modal is not for you!',
                    ephemeral: true
                });
            }

            const token = interaction.fields.getTextInputValue('spotify_token').trim();

            if (!token) {
                return interaction.reply({
                    content: '❌ Please provide a valid Spotify token.',
                    ephemeral: true
                });
            }

            try {
                // Defer the reply since we need to make API calls
                await interaction.deferReply({ ephemeral: true });

                // Ensure user data exists
                await ensureUserData(userId);

                // Test the token by making a request to Spotify API
                const response = await axios.get('https://api.spotify.com/v1/me', {
                    headers: {
                        'Authorization': `Bearer ${token}`
                    }
                });

                const profile = response.data;

                // Save the token and profile data
                const spotifyData = {
                    accessToken: token,
                    refreshToken: null, // Access tokens from console don't have refresh tokens
                    tokenExpiry: new Date(Date.now() + (60 * 60 * 1000)), // 1 hour from now
                    spotifyId: profile.id,
                    displayName: profile.display_name,
                    email: profile.email,
                    country: profile.country,
                    product: profile.product,
                    connectedAt: new Date()
                };

                await userCache.setSpotifyData(userId, spotifyData);

                // Send success message
                await interaction.editReply({
                    content: `✅ **Successfully connected to Spotify!**\n\n` +
                            `**Account:** ${profile.display_name}\n` +
                            `**Spotify ID:** ${profile.id}\n` +
                            `**Country:** ${profile.country}\n` +
                            `**Product:** ${profile.product}\n\n` +
                            `You can now use all Spotify commands! Try \`,spotify\` to see your current song.`
                });

                // Also send a success message in the channel
                const message = interaction.message;
                if (message && message.channel) {
                    await embeds.success({
                        author: interaction.user,
                        channel: message.channel
                    }, `Successfully connected **${profile.display_name}** to Spotify! 🎵`);
                }

            } catch (error) {
                console.error('Spotify token validation error:', error);
                
                let errorMessage = 'Failed to validate Spotify token.';
                
                if (error.response?.status === 401) {
                    errorMessage = 'Invalid Spotify token. Please make sure you copied the correct access token from the Spotify Web API Console.';
                } else if (error.response?.status === 403) {
                    errorMessage = 'Token access denied. Please make sure your token has the required scopes.';
                } else if (error.response?.status === 429) {
                    errorMessage = 'Too many requests. Please wait a moment and try again.';
                }

                await interaction.editReply({
                    content: `❌ ${errorMessage}\n\n**How to get your token:**\n` +
                            `1. Go to [Spotify Web API Console](https://developer.spotify.com/console/get-current-user/)\n` +
                            `2. Click **Get Token** and select all scopes\n` +
                            `3. Copy the generated access token\n` +
                            `4. Try the login command again`
                });
            }
        }
    }
};
