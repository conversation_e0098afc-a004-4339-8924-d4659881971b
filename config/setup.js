require('dotenv').config();

module.exports = {

  bot: {
    token: process.env.BOT_TOKEN,
    ownerId: process.env.BOT_OWNER_ID,
    devServerId: process.env.DEV_SERVER_ID,

    defaultPrefix: ',',
    supportServer: 'https://discord.gg/nWq5mwmsf7',
    inviteLink: 'https://discord.com/oauth2/authorize?client_id=1378983071650680872&permissions=8&scope=bot'
  },

  database: {
    connectionString: process.env.MONGODB_CONNECTION
  },

  apis: {
    lastfm: process.env.LASTFM_API_KEY,
    deepinfra: process.env.DEEP_API,
    twitch: {
      clientId: process.env.TWITCH_CLIENT_ID,
      clientSecret: process.env.TWITCH_CLIENT_SECRET
    },
    spotify: {
      clientId: process.env.SPOTIFY_CLIENT_ID,
      clientSecret: process.env.SPOTIFY_CLIENT_SECRET,
      redirectUri: process.env.SPOTIFY_REDIRECT_URI || 'http://localhost:3000/callback'
    }
  },

  logging: {
    errorWebhookUrl: "https://discord.com/api/webhooks/1381355913511964703/EnacdRicCeORjgYeW2qh4HmMA3v6PkHld3uHDlzO_JCW6nPYoajvVuUx_Ba712J0D4Rh",
    guildWebhookUrl: "https://discord.com/api/webhooks/1381345842241339493/oquRS0_5R8s7M7O9C0LLiiG4IHNlALSCw4Y-qJ-SZURuuhsWzJ3z2TNKFsPyA68MfaAb"
  },

  // Colors Configuration
  colors: {
    cooldown: '#a5eb78',
    success: '#a4ec7c',
    error: '#fc6464',
    warn: '#fbd03b',
    info: '#7a9fb0',
    embed: '#7a9fb0'
  },

  // Emojis Configuration
  emojis: {
    cooldown: '<:cooldown:1379296257746538677>',
    success: '<:success:1379289220606070834>',
    error: '<:error:1379296270438371419>',
    warn: '<:warn:1379296346334433316>',
    left: '<:left:1380275840004132864>',
    right: '<:right:1379296316081045595>',
    cancel: '<:cancel:1379296264008634486>',
    choose: '<:choose:1379296334321942690>',
    loading: '<a:loading:1379296276356665364>',
    lastfm: '<:lastfm:1379296282690195496>',
    spotify: '🎵',
    join: '<:success:1379289220606070834>',
    leave: '<:error:1379296270438371419>',
    cross: '<:x:1382101646598738111>',
    circle: '<:o:1382101649732014080>'

  },



};
