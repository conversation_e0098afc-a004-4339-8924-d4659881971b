// Integrated Spotify OAuth + Discord Bot
const { Client, Collection, GatewayIntentBits, Partials } = require('discord.js');
const express = require('express');
const config = require('./config/setup');

console.log('🚀 Starting Adore with integrated Spotify OAuth server...\n');

// Start Express server for Spotify OAuth
const app = express();
const PORT = process.env.PORT || 9871;
const SERVER_HOST = process.env.SERVER_HOST || 'your-server.pylex.com';

// Serve static files
app.use(express.static('adore-web'));

// Spotify callback route
app.get('/spotify/callback', (req, res) => {
    res.send(`
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Adore - Spotify Connected!</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #1db954, #1ed760);
            margin: 0;
            padding: 0;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .container {
            background: white;
            border-radius: 20px;
            padding: 40px;
            max-width: 600px;
            text-align: center;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        .header {
            color: #1db954;
            font-size: 2.5em;
            margin-bottom: 20px;
        }
        .token-container {
            background: #f8f9fa;
            border: 2px solid #1db954;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            word-break: break-all;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            line-height: 1.5;
        }
        .copy-button {
            background: #1db954;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 25px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 10px;
        }
        .copy-button:hover {
            background: #1ed760;
            transform: translateY(-2px);
        }
        .copy-button.copied {
            background: #28a745;
        }
        .instructions {
            background: #e8f5e8;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            text-align: left;
        }
        .step {
            margin: 10px 0;
            padding: 10px;
            background: white;
            border-radius: 5px;
            border-left: 4px solid #1db954;
        }
        .discord-button {
            background: #5865f2;
            color: white;
            text-decoration: none;
            padding: 12px 24px;
            border-radius: 25px;
            font-weight: bold;
            display: inline-block;
            margin: 10px;
            transition: all 0.3s ease;
        }
        .discord-button:hover {
            background: #4752c4;
            transform: translateY(-2px);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">🎵 Spotify Connected!</div>
        <p style="font-size: 18px; color: #666;">Your Spotify access token has been generated successfully!</p>

        <div id="token-section">
            <h3>🔑 Your Access Token</h3>
            <div class="token-container" id="token-display">
                <span id="token-text">Loading token...</span>
            </div>
            <button class="copy-button" id="copy-button" onclick="copyToken()">📋 Copy Token</button>
        </div>

        <div class="instructions">
            <h3>📱 Next Steps:</h3>
            <div class="step"><strong>1.</strong> Copy your token above (click the copy button)</div>
            <div class="step"><strong>2.</strong> Go back to Discord where you ran <code>,spotify login</code></div>
            <div class="step"><strong>3.</strong> Click the <strong>"Add Token"</strong> button in the bot's message</div>
            <div class="step"><strong>4.</strong> Paste your token and submit</div>
            <div class="step"><strong>5.</strong> Start using Spotify commands! Try <code>,spotify</code></div>
        </div>

        <a href="#" class="discord-button" onclick="window.close()">🔙 Back to Discord</a>

        <p style="color: #666; font-size: 14px; margin-top: 20px;">
            ⚠️ This token expires after 1 hour. Run <code>,spotify login</code> again to refresh.
        </p>
    </div>

    <script>
        function extractToken() {
            const hash = window.location.hash.substring(1);
            const params = new URLSearchParams(hash);
            const token = params.get('access_token');
            const error = params.get('error');

            if (error) {
                document.getElementById('token-text').textContent = 'Error: ' + error;
                document.getElementById('copy-button').style.display = 'none';
                return;
            }

            if (token) {
                document.getElementById('token-text').textContent = token;
                window.spotifyToken = token;
            } else {
                document.getElementById('token-text').textContent = 'No token found in URL';
                document.getElementById('copy-button').style.display = 'none';
            }
        }

        function copyToken() {
            const token = window.spotifyToken;
            if (!token) return;

            navigator.clipboard.writeText(token).then(() => {
                const button = document.getElementById('copy-button');
                const originalText = button.textContent;
                button.textContent = '✅ Copied!';
                button.classList.add('copied');

                setTimeout(() => {
                    button.textContent = originalText;
                    button.classList.remove('copied');
                }, 2000);
            }).catch(err => {
                const textArea = document.createElement('textarea');
                textArea.value = token;
                document.body.appendChild(textArea);
                textArea.select();
                document.execCommand('copy');
                document.body.removeChild(textArea);

                const button = document.getElementById('copy-button');
                const originalText = button.textContent;
                button.textContent = '✅ Copied!';
                button.classList.add('copied');

                setTimeout(() => {
                    button.textContent = originalText;
                    button.classList.remove('copied');
                }, 2000);
            });
        }

        document.addEventListener('DOMContentLoaded', extractToken);
    </script>
</body>
</html>
    `);
});

// Health check route
app.get('/health', (req, res) => {
    res.json({ status: 'ok', service: 'adore-spotify-auth' });
});

// Root route
app.get('/', (req, res) => {
    res.send(`
<!DOCTYPE html>
<html>
<head>
    <title>Adore Spotify Integration</title>
    <style>
        body { font-family: Arial, sans-serif; text-align: center; padding: 50px; }
        h1 { color: #1db954; }
    </style>
</head>
<body>
    <h1>🎵 Adore Spotify Integration</h1>
    <p>This server handles Spotify OAuth for the Adore Discord bot.</p>
    <p>Use <code>,spotify login</code> in Discord to get started!</p>
</body>
</html>
    `);
});

// Start Express server
app.listen(PORT, '0.0.0.0', () => {
    console.log(`🎵 Spotify OAuth server running on port ${PORT}`);
    console.log(`🌐 Callback URL: http://${SERVER_HOST}:${PORT}/spotify/callback`);
    console.log(`🔗 Health check: http://${SERVER_HOST}:${PORT}/health`);
    if (SERVER_HOST === 'your-server.pylex.com') {
        console.log(`📝 Remember to set SERVER_HOST in your .env file!`);
    }
});

// Now start the Discord bot
require('./index.js');
