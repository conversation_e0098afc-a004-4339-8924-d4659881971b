const { spawn } = require('child_process');
const path = require('path');

console.log('🚀 Starting Adore with Spotify OAuth server...\n');

// Start the Spotify OAuth server
const spotifyServer = spawn('node', ['spotify-server.js'], {
    stdio: 'pipe',
    cwd: __dirname
});

spotifyServer.stdout.on('data', (data) => {
    console.log(`[SPOTIFY] ${data.toString().trim()}`);
});

spotifyServer.stderr.on('data', (data) => {
    console.error(`[SPOTIFY ERROR] ${data.toString().trim()}`);
});

// Wait a moment for the server to start
setTimeout(() => {
    // Start the main bot
    const bot = spawn('node', ['--no-deprecation', 'index.js'], {
        stdio: 'pipe',
        cwd: __dirname
    });

    bot.stdout.on('data', (data) => {
        console.log(`[BOT] ${data.toString().trim()}`);
    });

    bot.stderr.on('data', (data) => {
        console.error(`[BOT ERROR] ${data.toString().trim()}`);
    });

    bot.on('close', (code) => {
        console.log(`[BOT] Process exited with code ${code}`);
        // Kill Spotify server if bot dies
        spotifyServer.kill();
        process.exit(code);
    });

}, 2000);

spotifyServer.on('close', (code) => {
    console.log(`[SPOTIFY] Server exited with code ${code}`);
});

// Handle process termination
process.on('SIGINT', () => {
    console.log('\n🛑 Shutting down...');
    spotifyServer.kill();
    process.exit(0);
});

process.on('SIGTERM', () => {
    console.log('\n🛑 Shutting down...');
    spotifyServer.kill();
    process.exit(0);
});
