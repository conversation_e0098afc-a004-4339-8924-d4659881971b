const express = require('express');
const axios = require('axios');
const config = require('./config/setup');
const { User } = require('./database');
const userCache = require('./database/cache/models/user');

const app = express();
const PORT = process.env.PORT || 3000;

// Serve a simple HTML page for the callback
app.get('/callback', async (req, res) => {
    const { code, state, error } = req.query;

    if (error) {
        return res.send(`
            <html>
                <head><title>Spotify Authorization Failed</title></head>
                <body style="font-family: Arial, sans-serif; text-align: center; padding: 50px;">
                    <h1 style="color: #e74c3c;">Authorization Failed</h1>
                    <p>Error: ${error}</p>
                    <p>You can close this window and try again.</p>
                </body>
            </html>
        `);
    }

    if (!code || !state) {
        return res.send(`
            <html>
                <head><title>Invalid Request</title></head>
                <body style="font-family: Arial, sans-serif; text-align: center; padding: 50px;">
                    <h1 style="color: #e74c3c;">Invalid Request</h1>
                    <p>Missing authorization code or state parameter.</p>
                    <p>You can close this window and try again.</p>
                </body>
            </html>
        `);
    }

    try {
        // Decode state to get user ID
        const stateData = Buffer.from(state, 'base64').toString('utf-8');
        const [userId, timestamp] = stateData.split('-');

        // Check if state is still valid (within 10 minutes)
        const now = Date.now();
        const stateTimestamp = parseInt(timestamp);
        if (now - stateTimestamp > 10 * 60 * 1000) {
            return res.send(`
                <html>
                    <head><title>Authorization Expired</title></head>
                    <body style="font-family: Arial, sans-serif; text-align: center; padding: 50px;">
                        <h1 style="color: #f39c12;">Authorization Expired</h1>
                        <p>The authorization link has expired. Please try again.</p>
                        <p>You can close this window and run the login command again.</p>
                    </body>
                </html>
            `);
        }

        // Exchange code for tokens
        const tokenResponse = await axios.post('https://accounts.spotify.com/api/token', 
            new URLSearchParams({
                grant_type: 'authorization_code',
                code: code,
                redirect_uri: config.apis.spotify.redirectUri,
                client_id: config.apis.spotify.clientId,
                client_secret: config.apis.spotify.clientSecret
            }),
            {
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded'
                }
            }
        );

        const tokens = tokenResponse.data;

        // Get user profile
        const profileResponse = await axios.get('https://api.spotify.com/v1/me', {
            headers: {
                'Authorization': `Bearer ${tokens.access_token}`
            }
        });

        const profile = profileResponse.data;

        // Calculate token expiry
        const tokenExpiry = new Date(Date.now() + (tokens.expires_in * 1000));

        // Save to database
        const spotifyData = {
            accessToken: tokens.access_token,
            refreshToken: tokens.refresh_token,
            tokenExpiry: tokenExpiry,
            spotifyId: profile.id,
            displayName: profile.display_name,
            email: profile.email,
            country: profile.country,
            product: profile.product,
            connectedAt: new Date()
        };

        await userCache.setSpotifyData(userId, spotifyData);

        // Success page
        res.send(`
            <html>
                <head><title>Spotify Connected Successfully</title></head>
                <body style="font-family: Arial, sans-serif; text-align: center; padding: 50px;">
                    <h1 style="color: #1db954;">🎵 Successfully Connected!</h1>
                    <div style="background: #f8f9fa; padding: 20px; border-radius: 10px; margin: 20px auto; max-width: 400px;">
                        <h3>Account Details</h3>
                        <p><strong>Display Name:</strong> ${profile.display_name}</p>
                        <p><strong>Spotify ID:</strong> ${profile.id}</p>
                        <p><strong>Country:</strong> ${profile.country}</p>
                        <p><strong>Product:</strong> ${profile.product}</p>
                    </div>
                    <p style="color: #666;">You can now close this window and use Spotify commands in Discord!</p>
                    <p style="font-size: 14px; color: #999;">Try: <code>,spotify</code> to see your current playing song</p>
                </body>
            </html>
        `);

    } catch (error) {
        console.error('Spotify callback error:', error);
        res.send(`
            <html>
                <head><title>Connection Failed</title></head>
                <body style="font-family: Arial, sans-serif; text-align: center; padding: 50px;">
                    <h1 style="color: #e74c3c;">Connection Failed</h1>
                    <p>There was an error connecting your Spotify account.</p>
                    <p style="color: #666;">Error: ${error.message}</p>
                    <p>You can close this window and try again.</p>
                </body>
            </html>
        `);
    }
});

// Health check endpoint
app.get('/', (req, res) => {
    res.send(`
        <html>
            <head><title>Adore Spotify Integration</title></head>
            <body style="font-family: Arial, sans-serif; text-align: center; padding: 50px;">
                <h1>🎵 Adore Spotify Integration</h1>
                <p>This server handles Spotify OAuth callbacks for the Adore Discord bot.</p>
                <p style="color: #666;">Use the <code>,spotify login</code> command in Discord to get started.</p>
            </body>
        </html>
    `);
});

// Start server
app.listen(PORT, () => {
    console.log(`Spotify callback server running on port ${PORT}`);
    console.log(`Callback URL: http://localhost:${PORT}/callback`);
});

module.exports = app;
