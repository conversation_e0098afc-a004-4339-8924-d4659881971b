const { embeds } = require('../../core/embeds');
const spotifyAPI = require('../../utils/spotify');

module.exports = {
    name: "previous",
    aliases: ['prev', 'back'],
    description: `Skip to the previous song`,
    usage: '{guildprefix}spotify previous',
    cooldown: 2000,
    run: async (client, message, args) => {
        try {
            // Check if user is authenticated
            const userData = await require('../../database/cache/models/user').getUser(message.author.id);
            if (!userData?.Spotify?.accessToken) {
                return embeds.warn(message, `You need to connect your Spotify account first\n\nUse \`spotify login\` to get started.`);
            }

            // Get current playback state
            const playback = await spotifyAPI.getCurrentPlayback(message.author.id);
            if (!playback) {
                return embeds.warn(message, `No active Spotify session found\n\nMake sure Spotify is open and playing music on one of your devices.`);
            }

            // Get current track info before going back
            const currentTrack = playback.item;
            const currentArtists = currentTrack.artists.map(artist => artist.name).join(', ');

            // Skip to previous track
            await spotifyAPI.makeRequest(message.author.id, '/me/player/previous', 'POST');

            // Wait a moment for Spotify to update, then get new track info
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            try {
                const newPlayback = await spotifyAPI.getCurrentPlayback(message.author.id);
                if (newPlayback && newPlayback.item) {
                    const newTrack = newPlayback.item;
                    const newArtists = newTrack.artists.map(artist => artist.name).join(', ');
                    
                    await embeds.success(message, `⏮️ Went back from **${currentTrack.name}** by **${currentArtists}**\n\nNow playing: **${newTrack.name}** by **${newArtists}**`);
                } else {
                    await embeds.success(message, `⏮️ Went back from **${currentTrack.name}** by **${currentArtists}**`);
                }
            } catch (error) {
                // If we can't get the new track info, just show that we went back
                await embeds.success(message, `⏮️ Went back from **${currentTrack.name}** by **${currentArtists}**`);
            }

        } catch (error) {
            console.error('Spotify previous error:', error);
            
            if (error.message.includes('authentication expired')) {
                return embeds.warn(message, error.message);
            }
            
            if (error.message.includes('No active device')) {
                return embeds.warn(message, `No active Spotify device found\n\nMake sure Spotify is open and playing music on one of your devices.`);
            }

            return embeds.error(message, `Failed to skip to previous track: ${error.message}`);
        }
    }
};
