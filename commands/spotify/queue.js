const { EmbedBuilder } = require('discord.js');
const { embeds } = require('../../core/embeds');
const { colors, emojis } = require('../../config/setup');
const spotifyAPI = require('../../utils/spotify');

module.exports = {
    name: "queue",
    aliases: ['add', 'q'],
    description: `Add a song to your Spotify queue`,
    usage: '{guildprefix}spotify queue [song name/artist]',
    cooldown: 3000,
    run: async (client, message, args) => {
        try {
            // Check if user is authenticated
            const userData = await require('../../database/cache/models/user').getUser(message.author.id);
            if (!userData?.Spotify?.accessToken) {
                return embeds.warn(message, `You need to connect your Spotify account first\n\nUse \`spotify login\` to get started.`);
            }

            if (!args.length) {
                return embeds.warn(message, `Please provide a song name or artist to search for\n\nExample: \`spotify queue bohemian rhapsody\``);
            }

            const query = args.join(' ');

            // Show loading message
            const loadingEmbed = new EmbedBuilder()
                .setColor(colors.info)
                .setDescription(`${emojis.loading} Searching for **${query}**...`);
            
            const loadingMessage = await message.channel.send({ embeds: [loadingEmbed] });

            try {
                // Search for tracks
                const searchResults = await spotifyAPI.search(message.author.id, query, 'track', 5);
                
                if (!searchResults.tracks || !searchResults.tracks.items.length) {
                    await loadingMessage.edit({ embeds: [embeds.warn(message, `No tracks found for **${query}**\n\nTry a different search term.`, false)] });
                    return;
                }

                const track = searchResults.tracks.items[0]; // Get the first result
                const artists = track.artists.map(artist => artist.name).join(', ');

                // Check if user has an active device
                const playback = await spotifyAPI.getCurrentPlayback(message.author.id);
                if (!playback) {
                    await loadingMessage.edit({ 
                        embeds: [embeds.warn(message, `No active Spotify session found\n\nMake sure Spotify is open and playing music on one of your devices.`, false)] 
                    });
                    return;
                }

                // Add to queue
                await spotifyAPI.makeRequest(message.author.id, `/me/player/queue?uri=${encodeURIComponent(track.uri)}`, 'POST');

                // Create success embed
                const successEmbed = new EmbedBuilder()
                    .setColor(colors.success)
                    .setTitle('➕ Added to Queue')
                    .setDescription(`**${track.name}**\nby **${artists}**`)
                    .addFields([
                        {
                            name: '💿 Album',
                            value: track.album.name,
                            inline: true
                        },
                        {
                            name: '⏱️ Duration',
                            value: spotifyAPI.formatDuration(track.duration_ms),
                            inline: true
                        },
                        {
                            name: '🔗 Spotify',
                            value: `[Open in Spotify](${track.external_urls.spotify})`,
                            inline: true
                        }
                    ]);

                if (track.album.images && track.album.images.length > 0) {
                    successEmbed.setThumbnail(track.album.images[0].url);
                }

                await loadingMessage.edit({ embeds: [successEmbed] });

            } catch (searchError) {
                console.error('Spotify queue search error:', searchError);
                
                if (searchError.message.includes('authentication expired')) {
                    await loadingMessage.edit({ embeds: [embeds.warn(message, searchError.message, false)] });
                    return;
                }
                
                if (searchError.message.includes('No active device')) {
                    await loadingMessage.edit({ 
                        embeds: [embeds.warn(message, `No active Spotify device found\n\nMake sure Spotify is open and playing music on one of your devices.`, false)] 
                    });
                    return;
                }

                await loadingMessage.edit({ 
                    embeds: [embeds.error(message, `Failed to add track to queue: ${searchError.message}`, false)] 
                });
            }

        } catch (error) {
            console.error('Spotify queue error:', error);
            return embeds.error(message, `Failed to process queue request: ${error.message}`);
        }
    }
};
