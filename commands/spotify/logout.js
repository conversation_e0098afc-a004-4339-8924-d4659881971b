const { embeds } = require('../../core/embeds');
const { User } = require('../../database');
const userCache = require('../../database/cache/models/user');
const { createConfirmation } = require('../../core/buttons');

module.exports = {
    name: "logout",
    aliases: ['disconnect', 'unlink'],
    description: `Disconnect your Spotify account from the bot`,
    usage: '{guildprefix}spotify logout',
    cooldown: 3000,
    run: async (client, message, args) => {
        try {
            // Check if user is connected
            const userData = await userCache.getUser(message.author.id);
            if (!userData?.Spotify?.accessToken) {
                return embeds.warn(message, `You don't have a Spotify account connected to **adore**\n\nUse \`spotify login\` to connect your account.`);
            }

            // Create confirmation
            await createConfirmation(
                message,
                `Are you sure you want to disconnect your Spotify account?\n\nThis will remove all stored Spotify data and you'll need to re-authorize to use Spotify commands again.`,
                async (interaction) => {
                    try {
                        // Remove Spotify data from database
                        await User.updateOne(
                            { UserID: message.author.id },
                            {
                                $unset: {
                                    'Spotify.accessToken': '',
                                    'Spotify.refreshToken': '',
                                    'Spotify.tokenExpiry': '',
                                    'Spotify.spotifyId': '',
                                    'Spotify.displayName': '',
                                    'Spotify.email': '',
                                    'Spotify.country': '',
                                    'Spotify.product': '',
                                    'Spotify.connectedAt': ''
                                }
                            }
                        );

                        // Update cache
                        await userCache.updateUser(message.author.id, {
                            'Spotify.accessToken': null,
                            'Spotify.refreshToken': null,
                            'Spotify.tokenExpiry': null,
                            'Spotify.spotifyId': null,
                            'Spotify.displayName': null,
                            'Spotify.email': null,
                            'Spotify.country': null,
                            'Spotify.product': null,
                            'Spotify.connectedAt': null
                        });

                        // Send success message
                        await embeds.success(message, `Successfully disconnected your Spotify account from **adore**\n\nTo completely remove access, visit your [Spotify account settings](https://www.spotify.com/account/apps/) and revoke access to **adore**.`);

                    } catch (error) {
                        console.error('Spotify logout error:', error);
                        await embeds.error(message, `Failed to disconnect Spotify account: ${error.message}`);
                    }
                }
            );

        } catch (error) {
            console.error('Spotify logout error:', error);
            return embeds.error(message, `Failed to process logout request: ${error.message}`);
        }
    }
};
