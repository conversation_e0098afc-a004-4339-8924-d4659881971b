const { embeds } = require('../../core/embeds');
const spotifyAPI = require('../../utils/spotify');

module.exports = {
    name: "pause",
    aliases: ['stop'],
    description: `Pause the current song`,
    usage: '{guildprefix}spotify pause',
    cooldown: 2000,
    run: async (client, message, args) => {
        try {
            // Check if user is authenticated
            const userData = await require('../../database/cache/models/user').getUser(message.author.id);
            if (!userData?.Spotify?.accessToken) {
                return embeds.warn(message, `You need to connect your Spotify account first\n\nUse \`spotify login\` to get started.`);
            }

            // Get current playback state
            const playback = await spotifyAPI.getCurrentPlayback(message.author.id);
            if (!playback) {
                return embeds.warn(message, `No active Spotify session found\n\nMake sure Spotify is open and playing music on one of your devices.`);
            }

            if (!playback.is_playing) {
                return embeds.warn(message, `Spotify is already paused\n\nUse \`spotify resume\` to continue playback.`);
            }

            // Pause playback
            await spotifyAPI.makeRequest(message.author.id, '/me/player/pause', 'PUT');

            // Get track info for response
            const track = playback.item;
            const artists = track.artists.map(artist => artist.name).join(', ');

            await embeds.success(message, `⏸️ Paused **${track.name}** by **${artists}**`);

        } catch (error) {
            console.error('Spotify pause error:', error);
            
            if (error.message.includes('authentication expired')) {
                return embeds.warn(message, error.message);
            }
            
            if (error.message.includes('No active device')) {
                return embeds.warn(message, `No active Spotify device found\n\nMake sure Spotify is open and playing music on one of your devices.`);
            }

            return embeds.error(message, `Failed to pause playback: ${error.message}`);
        }
    }
};
