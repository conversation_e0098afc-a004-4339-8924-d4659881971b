const { embeds } = require('../../core/embeds');
const spotifyAPI = require('../../utils/spotify');

module.exports = {
    name: "like",
    aliases: ['save', 'heart'],
    description: `Like the currently playing song`,
    usage: '{guildprefix}spotify like',
    cooldown: 2000,
    run: async (client, message, args) => {
        try {
            // Check if user is authenticated
            const userData = await require('../../database/cache/models/user').getUser(message.author.id);
            if (!userData?.Spotify?.accessToken) {
                return embeds.warn(message, `You need to connect your Spotify account first\n\nUse \`spotify login\` to get started.`);
            }

            // Get current playback state
            const playback = await spotifyAPI.getCurrentPlayback(message.author.id);
            if (!playback) {
                return embeds.warn(message, `No active Spotify session found\n\nMake sure Spotify is open and playing music on one of your devices.`);
            }

            if (!playback.item) {
                return embeds.warn(message, `No track is currently playing\n\nStart playing a song first.`);
            }

            const track = playback.item;
            const artists = track.artists.map(artist => artist.name).join(', ');

            // Check if track is already liked
            try {
                const isLiked = await spotifyAPI.makeRequest(message.author.id, `/me/tracks/contains?ids=${track.id}`);
                if (isLiked[0]) {
                    return embeds.warn(message, `💚 **${track.name}** by **${artists}** is already in your liked songs\n\nUse \`spotify unlike\` to remove it.`);
                }
            } catch (checkError) {
                // If we can't check, proceed with liking anyway
                console.warn('Could not check if track is already liked:', checkError.message);
            }

            // Like the track
            await spotifyAPI.makeRequest(message.author.id, `/me/tracks?ids=${track.id}`, 'PUT');

            await embeds.success(message, `💚 Liked **${track.name}** by **${artists}**\n\nAdded to your Liked Songs playlist.`);

        } catch (error) {
            console.error('Spotify like error:', error);
            
            if (error.message.includes('authentication expired')) {
                return embeds.warn(message, error.message);
            }
            
            if (error.message.includes('No active device')) {
                return embeds.warn(message, `No active Spotify device found\n\nMake sure Spotify is open and playing music on one of your devices.`);
            }

            return embeds.error(message, `Failed to like track: ${error.message}`);
        }
    }
};
