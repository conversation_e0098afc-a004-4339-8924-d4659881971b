const { <PERSON><PERSON><PERSON><PERSON><PERSON>, ActionRowBuilder, ButtonBuilder, ButtonStyle, ModalBuilder, TextInputBuilder, TextInputStyle } = require('discord.js');
const { embeds } = require('../../core/embeds');
const { colors, emojis } = require('../../config/setup');
const spotifyAPI = require('../../utils/spotify');
const { User, ensureUserData } = require('../../database');
const userCache = require('../../database/cache/models/user');

module.exports = {
    name: "login",
    aliases: ['connect', 'auth'],
    description: `Connect your Spotify account to the bot`,
    usage: '{guildprefix}spotify login',
    cooldown: 5000,
    run: async (client, message, args) => {
        try {
            // Ensure user data exists
            await ensureUserData(message.author.id);

            // Check if user is already connected
            const spotifyData = await userCache.getSpotifyData(message.author.id);
            if (spotifyData?.accessToken) {
                try {
                    const profile = await spotifyAPI.getUserProfile(message.author.id);
                    return embeds.warn(message, `You're already connected to Spotify as **${profile.display_name}**\n\nUse \`spotify logout\` to disconnect first.`);
                } catch (error) {
                    // If we can't get profile, token might be expired, continue with login
                }
            }

            // Create authorization embed with instructions
            const authEmbed = new EmbedBuilder()
                .setColor(colors.info)
                .setTitle('🎵 Spotify Authorization')
                .setDescription(`Connect your Spotify account to **adore** to control your music and view statistics.\n\n**How to get your token:**`)
                .addFields([
                    {
                        name: '📱 Step 1: Create Spotify App',
                        value: `1. Go to [Spotify Developer Dashboard](https://developer.spotify.com/dashboard)\n2. Click **Create App**\n3. Fill in any name/description\n4. Set redirect URI to: \`http://localhost:8888/callback\`\n5. Save your **Client ID** and **Client Secret**`,
                        inline: false
                    },
                    {
                        name: '🔑 Step 2: Get Access Token',
                        value: `1. Visit [Spotify Web API Console](https://developer.spotify.com/console/get-current-user/)\n2. Click **Get Token**\n3. Select all scopes you need\n4. Copy the generated token`,
                        inline: false
                    },
                    {
                        name: '🔐 Step 3: Add Token',
                        value: `Click the button below to securely add your token`,
                        inline: false
                    },
                    {
                        name: '⚠️ Security Note',
                        value: `Your token is stored securely and only used for Spotify API requests. Tokens expire after 1 hour but you can regenerate them anytime.`,
                        inline: false
                    }
                ])
                .setFooter({ text: 'Tokens expire after 1 hour - you can regenerate them anytime' })
                .setTimestamp();

            // Create button to open modal
            const authButton = new ActionRowBuilder()
                .addComponents(
                    new ButtonBuilder()
                        .setCustomId(`spotify_token_${message.author.id}`)
                        .setLabel('Add Spotify Token')
                        .setStyle(ButtonStyle.Primary)
                        .setEmoji('🎵')
                );

            const sentMessage = await message.channel.send({
                embeds: [authEmbed],
                components: [authButton]
            });

            // Create collector for button interaction
            const collector = sentMessage.createMessageComponentCollector({
                time: 300000 // 5 minutes
            });

            collector.on('collect', async (interaction) => {
                if (!interaction.customId.startsWith('spotify_token_')) return;

                const userId = interaction.customId.split('_')[2];
                if (interaction.user.id !== userId) {
                    return interaction.reply({
                        content: `${emojis.warn} This button is not for you!`,
                        ephemeral: true
                    });
                }

                // Create modal for token input
                const modal = new ModalBuilder()
                    .setCustomId(`spotify_modal_${userId}`)
                    .setTitle('🎵 Add Spotify Token');

                const tokenInput = new TextInputBuilder()
                    .setCustomId('spotify_token')
                    .setLabel('Spotify Token (sp_dc)')
                    .setStyle(TextInputStyle.Short)
                    .setPlaceholder('Paste your sp_dc token here...')
                    .setRequired(true)
                    .setMaxLength(500);

                const actionRow = new ActionRowBuilder().addComponents(tokenInput);
                modal.addComponents(actionRow);

                await interaction.showModal(modal);
            });

            collector.on('end', async () => {
                try {
                    const disabledButton = new ActionRowBuilder()
                        .addComponents(
                            new ButtonBuilder()
                                .setCustomId('disabled')
                                .setLabel('Add Spotify Token')
                                .setStyle(ButtonStyle.Secondary)
                                .setEmoji('🎵')
                                .setDisabled(true)
                        );

                    await sentMessage.edit({ components: [disabledButton] });
                } catch (error) {
                    // Message might have been deleted
                }
            });

        } catch (error) {
            console.error('Spotify login error:', error);
            return embeds.deny(message, `Failed to show login interface: ${error.message}`);
        }
    }
};
