const { <PERSON><PERSON><PERSON><PERSON><PERSON>, ActionRow<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, ButtonStyle } = require('discord.js');
const { embeds } = require('../../core/embeds');
const { colors, emojis } = require('../../config/setup');
const spotifyAPI = require('../../utils/spotify');
const { User, ensureUserData } = require('../../database');
const userCache = require('../../database/cache/models/user');

module.exports = {
    name: "login",
    aliases: ['connect', 'auth'],
    description: `Connect your Spotify account to the bot`,
    usage: '{guildprefix}spotify login',
    cooldown: 5000,
    run: async (client, message, args) => {
        try {
            // Ensure user data exists
            await ensureUserData(message.author.id);

            // Check if user is already connected
            const spotifyData = await userCache.getSpotifyData(message.author.id);
            if (spotifyData?.accessToken) {
                try {
                    const profile = await spotifyAPI.getUserProfile(message.author.id);
                    return embeds.warn(message, `You're already connected to Spotify as **${profile.display_name}**\n\nUse \`spotify logout\` to disconnect first.`);
                } catch (error) {
                    // If we can't get profile, token might be expired, continue with login
                }
            }

            // Generate state parameter for security
            const state = Buffer.from(`${message.author.id}-${Date.now()}`).toString('base64');
            
            // Get authorization URL
            const authURL = spotifyAPI.getAuthURL(state);

            // Create authorization embed
            const authEmbed = new EmbedBuilder()
                .setColor(colors.info)
                .setTitle('🎵 Spotify Authorization')
                .setDescription(`Authorize **adore** to access your Spotify account here. You give us permission to control your active player and show your music statistics through commands.\n\nIf you want to remove your account with **adore**, run \`spotify logout\` and visit your settings on Spotify to unauthorize our application.`)
                .addFields([
                    {
                        name: '🔗 Authorization Link',
                        value: `[Click here to authorize](${authURL})`,
                        inline: false
                    },
                    {
                        name: '⚠️ Important',
                        value: 'After authorizing, you\'ll need to complete the setup process. The bot will guide you through it.',
                        inline: false
                    }
                ])
                .setFooter({ text: 'This authorization link expires in 10 minutes' })
                .setTimestamp();

            // Create button for authorization
            const authButton = new ActionRowBuilder()
                .addComponents(
                    new ButtonBuilder()
                        .setLabel('Authorize Spotify')
                        .setStyle(ButtonStyle.Link)
                        .setURL(authURL)
                        .setEmoji('🎵')
                );

            const sentMessage = await message.channel.send({
                embeds: [authEmbed],
                components: [authButton]
            });

            // Store the state and message info for later verification
            // In a production environment, you'd want to store this in a temporary cache/database
            // For now, we'll use a simple in-memory store with cleanup
            if (!client.spotifyAuthStates) {
                client.spotifyAuthStates = new Map();
            }

            client.spotifyAuthStates.set(state, {
                userId: message.author.id,
                messageId: sentMessage.id,
                channelId: message.channel.id,
                timestamp: Date.now()
            });

            // Clean up expired states (older than 10 minutes)
            const tenMinutesAgo = Date.now() - (10 * 60 * 1000);
            for (const [key, value] of client.spotifyAuthStates.entries()) {
                if (value.timestamp < tenMinutesAgo) {
                    client.spotifyAuthStates.delete(key);
                }
            }

            // Auto-delete the message after 10 minutes
            setTimeout(async () => {
                try {
                    await sentMessage.delete();
                    client.spotifyAuthStates?.delete(state);
                } catch (error) {
                    // Message might already be deleted
                }
            }, 10 * 60 * 1000);

        } catch (error) {
            console.error('Spotify login error:', error);
            return embeds.deny(message, `Failed to generate authorization link: ${error.message}`);
        }
    }
};
