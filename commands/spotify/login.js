const { <PERSON><PERSON><PERSON><PERSON><PERSON>, ActionRowBuilder, ButtonBuilder, ButtonStyle, ModalBuilder, TextInputBuilder, TextInputStyle } = require('discord.js');
const { embeds } = require('../../core/embeds');
const { colors, emojis } = require('../../config/setup');
const config = require('../../config/setup');
const spotifyAPI = require('../../utils/spotify');
const { User, ensureUserData } = require('../../database');
const userCache = require('../../database/cache/models/user');

module.exports = {
    name: "login",
    aliases: ['connect', 'auth'],
    description: `Connect your Spotify account to the bot`,
    usage: '{guildprefix}spotify login',
    cooldown: 5000,
    run: async (client, message, args) => {
        try {
            // Ensure user data exists
            await ensureUserData(message.author.id);

            // Check if user is already connected
            const spotifyData = await userCache.getSpotifyData(message.author.id);
            if (spotifyData?.accessToken) {
                try {
                    const profile = await spotifyAPI.getUserProfile(message.author.id);
                    return embeds.warn(message, `You're already connected to Spotify as **${profile.display_name}**\n\nUse \`spotify logout\` to disconnect first.`);
                } catch (error) {
                    // If we can't get profile, token might be expired, continue with login
                }
            }

            // Generate state parameter for security
            const state = Buffer.from(`${message.author.id}-${Date.now()}`).toString('base64');

            // Create Spotify authorization URL
            const scopes = [
                'user-read-private',
                'user-read-email',
                'user-read-playback-state',
                'user-modify-playback-state',
                'user-read-currently-playing',
                'user-library-read',
                'user-library-modify',
                'user-top-read',
                'playlist-read-private',
                'playlist-modify-public',
                'playlist-modify-private'
            ].join(' ');

            const authURL = `https://accounts.spotify.com/authorize?` +
                `response_type=token&` +
                `client_id=${config.apis.spotify.clientId}&` +
                `scope=${encodeURIComponent(scopes)}&` +
                `redirect_uri=${encodeURIComponent(config.apis.spotify.redirectUri)}&` +
                `state=${state}`;

            // Create authorization embed
            const authEmbed = new EmbedBuilder()
                .setColor(colors.info)
                .setTitle('🎵 Spotify Authorization')
                .setDescription(`Connect your Spotify account to **adore** to control your music and view statistics.`)
                .addFields([
                    {
                        name: '🚀 Super Easy Setup',
                        value: `**Step 1:** Click **"Authorize Spotify"** → Login & authorize\n**Step 2:** Copy your token from the website\n**Step 3:** Click **"Add Token"** → Paste & submit\n**Done!** 🎉`,
                        inline: false
                    },
                    {
                        name: '⚠️ Quick Notes',
                        value: `• Tokens last **1 hour** - just re-run this command to refresh\n• Your token is stored securely and only used for Spotify API requests\n• Disconnect anytime with \`spotify logout\``,
                        inline: false
                    }
                ])
                .setFooter({ text: 'Authorize → Copy → Add → Paste → Done! 🎵' })
                .setTimestamp();

            // Create buttons for authorization and token input
            const authButtons = new ActionRowBuilder()
                .addComponents(
                    new ButtonBuilder()
                        .setLabel('Authorize Spotify')
                        .setStyle(ButtonStyle.Link)
                        .setURL(authURL)
                        .setEmoji('🎵'),
                    new ButtonBuilder()
                        .setCustomId(`spotify_token_${message.author.id}`)
                        .setLabel('Add Token')
                        .setStyle(ButtonStyle.Primary)
                        .setEmoji('🔐')
                );

            const sentMessage = await message.channel.send({
                embeds: [authEmbed],
                components: [authButtons]
            });

            // Create collector for button interaction
            const collector = sentMessage.createMessageComponentCollector({
                time: 300000 // 5 minutes
            });

            collector.on('collect', async (interaction) => {
                if (!interaction.customId.startsWith('spotify_token_')) return;

                const userId = interaction.customId.split('_')[2];
                if (interaction.user.id !== userId) {
                    return interaction.reply({
                        content: `${emojis.warn} This button is not for you!`,
                        ephemeral: true
                    });
                }

                // Create modal for token input
                const modal = new ModalBuilder()
                    .setCustomId(`spotify_modal_${userId}`)
                    .setTitle('🎵 Add Spotify Token');

                const tokenInput = new TextInputBuilder()
                    .setCustomId('spotify_token')
                    .setLabel('Spotify Access Token')
                    .setStyle(TextInputStyle.Short)
                    .setPlaceholder('Paste your access token from the website here...')
                    .setRequired(true)
                    .setMaxLength(500);

                const actionRow = new ActionRowBuilder().addComponents(tokenInput);
                modal.addComponents(actionRow);

                await interaction.showModal(modal);
            });

            collector.on('end', async () => {
                try {
                    const disabledButtons = new ActionRowBuilder()
                        .addComponents(
                            new ButtonBuilder()
                                .setLabel('Authorize Spotify')
                                .setStyle(ButtonStyle.Secondary)
                                .setURL(authURL)
                                .setEmoji('🎵')
                                .setDisabled(true),
                            new ButtonBuilder()
                                .setCustomId('disabled')
                                .setLabel('Add Token')
                                .setStyle(ButtonStyle.Secondary)
                                .setEmoji('🔐')
                                .setDisabled(true)
                        );

                    await sentMessage.edit({ components: [disabledButtons] });
                } catch (error) {
                    // Message might have been deleted
                }
            });

        } catch (error) {
            console.error('Spotify login error:', error);
            return embeds.deny(message, `Failed to show login interface: ${error.message}`);
        }
    }
};
