const { EmbedBuilder } = require('discord.js');
const { embeds } = require('../../core/embeds');
const { colors, emojis } = require('../../config/setup');
const spotifyAPI = require('../../utils/spotify');

module.exports = {
    name: "toptracks",
    aliases: ['tracks', 'topsongs'],
    description: `Show your top tracks from Spotify`,
    usage: '{guildprefix}spotify toptracks [short/medium/long]',
    cooldown: 5000,
    run: async (client, message, args) => {
        try {
            // Check if user is authenticated
            const userData = await require('../../database/cache/models/user').getUser(message.author.id);
            if (!userData?.Spotify?.accessToken) {
                return embeds.warn(message, `You need to connect your Spotify account first\n\nUse \`spotify login\` to get started.`);
            }

            // Parse time range
            let timeRange = 'medium_term'; // Default to 6 months
            if (args.length > 0) {
                const input = args[0].toLowerCase();
                if (input === 'short' || input === '4w' || input === 'month') {
                    timeRange = 'short_term';
                } else if (input === 'medium' || input === '6m' || input === 'months') {
                    timeRange = 'medium_term';
                } else if (input === 'long' || input === 'all' || input === 'alltime') {
                    timeRange = 'long_term';
                }
            }

            // Show loading message
            const loadingEmbed = new EmbedBuilder()
                .setColor(colors.info)
                .setDescription(`${emojis.loading} Loading your top tracks...`);
            
            const loadingMessage = await message.channel.send({ embeds: [loadingEmbed] });

            try {
                // Get top tracks
                const topTracks = await spotifyAPI.getTopItems(message.author.id, 'tracks', timeRange, 10);
                
                if (!topTracks.items || !topTracks.items.length) {
                    await loadingMessage.edit({ 
                        embeds: [embeds.warn(message, `No top tracks found for the selected time period\n\nTry listening to more music on Spotify!`, false)] 
                    });
                    return;
                }

                // Get user profile for display name
                const profile = await spotifyAPI.getUserProfile(message.author.id);

                // Create embed
                const tracksEmbed = new EmbedBuilder()
                    .setColor(colors.info)
                    .setTitle(`🎵 ${profile.display_name}'s Top Tracks`)
                    .setDescription(`**${spotifyAPI.formatTimeRange(timeRange)}**\n\n`)
                    .setThumbnail(message.author.displayAvatarURL({ dynamic: true }));

                let trackList = '';
                topTracks.items.forEach((track, index) => {
                    const artists = track.artists.map(artist => artist.name).join(', ');
                    const duration = spotifyAPI.formatDuration(track.duration_ms);
                    
                    trackList += `**${index + 1}.** [${track.name}](${track.external_urls.spotify})\n`;
                    trackList += `by **${artists}** • ${duration}\n\n`;
                });

                tracksEmbed.setDescription(`**${spotifyAPI.formatTimeRange(timeRange)}**\n\n${trackList}`);
                
                // Add footer with additional info
                tracksEmbed.setFooter({ 
                    text: `Use "short", "medium", or "long" to change time period • Powered by Spotify` 
                });

                await loadingMessage.edit({ embeds: [tracksEmbed] });

            } catch (apiError) {
                console.error('Spotify top tracks API error:', apiError);
                
                if (apiError.message.includes('authentication expired')) {
                    await loadingMessage.edit({ embeds: [embeds.warn(message, apiError.message, false)] });
                    return;
                }

                await loadingMessage.edit({ 
                    embeds: [embeds.error(message, `Failed to get top tracks: ${apiError.message}`, false)] 
                });
            }

        } catch (error) {
            console.error('Spotify top tracks error:', error);
            return embeds.error(message, `Failed to process top tracks request: ${error.message}`);
        }
    }
};
