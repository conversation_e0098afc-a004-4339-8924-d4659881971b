<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no, viewport-fit=cover">
  <title>Embed Builder - EmbedCraft</title>
  <meta name="description" content="Create stunning Discord embeds with our professional embed builder. Advanced features, real-time preview, and seamless integration.">
  <link rel="stylesheet" href="css/main.css">
  <link rel="stylesheet" href="css/background.css">
  <link rel="stylesheet" href="css/homepage.css">
  <link rel="stylesheet" href="css/form.css">
  <link rel="stylesheet" href="css/preview.css">
  <link rel="stylesheet" href="css/builder.css">
  <link rel="stylesheet" href="css/performance.css">
  <link rel="stylesheet" href="css/no-bounce.css">
  <link rel="stylesheet" href="css/ios-fixes.css">
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
  <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">

</head>
<body class="builder-page">
  <!-- Background -->
  <div class="page-background">
    <div class="stars"></div>
    <div class="twinkling"></div>
    <div class="milky-way"></div>
  </div>

  <!-- Navigation -->
  <nav class="navbar" data-aos="fade-down" data-aos-delay="100">
    <div class="nav-links">
      <a href="index.html" class="nav-link">Home</a>
      <a href="index.html#features" class="nav-link">Features</a>
      <a href="builder.html" class="nav-link active">Builder</a>
      <a href="commands.html" class="nav-link">Commands</a>
      <a href="https://adore.gitbook.io/adore-docs" class="nav-link" target="_blank">Docs</a>
    </div>
    <div class="mobile-menu-toggle">
      <i class="fas fa-bars"></i>
    </div>
  </nav>

  <!-- Builder Hero Section -->
  <section id="home" class="hero-section">
    <div class="hero-background">
    </div>
    <div class="hero-container">
      <div class="hero-content">

        <h1 class="hero-title animate-on-load delay-200">
          <span class="gradient-text">Adore</span> <span class="gradient-text">Embed</span> Builder
        </h1>
        <p class="hero-description animate-on-load delay-300">
          Create stunning Discord embeds with Adore's powerful visual editor. Design, preview, and export your embeds in real-time with epic night sky vibes.
        </p>
        <div class="hero-actions animate-on-load delay-400">
          <button class="btn-primary hero-cta" onclick="document.getElementById('builder-main').scrollIntoView({behavior: 'smooth'})">
            <i class="fas fa-tools"></i>
            Start Building
          </button>
          <button class="btn-secondary" onclick="openImportModal()">
            <i class="fas fa-file-import"></i>
            Load Template
          </button>
        </div>

      </div>



    </div>
  </section>

  <!-- Builder Main Section -->
  <section class="builder-main" id="builder-main">
    <div class="embed-builder-container">
      <div class="embed-builder-layout">
        <!-- Left Panel - Form -->
        <div class="left-panel">
          <h1 class="builder-title">Embed Builder</h1>

          <form id="embedForm">
            <div class="color-section-wrapper">
              <div class="section-title">Color</div>
              <div class="color-section">
                <input type="color" id="color" class="color-swatch" value="#000000">
                <input type="text" id="colorInput" class="color-text-input" placeholder="Enter hex color" value="">
              </div>
            </div>

            <div class="section">
              <div class="section-title">Content</div>
              <textarea id="content" class="textarea large" placeholder="Message content (optional)"></textarea>
            </div>

            <div class="form-row">
              <div class="form-group">
                <div class="section-title">Author Name</div>
                <input type="text" id="authorName" class="input" placeholder="">
              </div>
              <div class="form-group">
                <div class="section-title">Author Icon URL</div>
                <input type="text" id="authorIcon" class="input" placeholder="">
              </div>
              <div class="form-group">
                <div class="section-title">Author URL</div>
                <input type="text" id="authorUrl" class="input" placeholder="">
              </div>
            </div>

            <div class="form-row">
              <div class="form-group">
                <div class="section-title">Title</div>
                <input type="text" id="title" class="input" placeholder="">
              </div>
              <div class="form-group">
                <div class="section-title">URL</div>
                <input type="text" id="url" class="input" placeholder="">
              </div>
            </div>

            <div class="section">
              <div class="section-title">Description</div>
              <textarea id="description" class="textarea large" placeholder=""></textarea>
            </div>

            <div class="form-row">
              <div class="form-group">
                <div class="section-title">Thumbnail URL</div>
                <input type="text" id="thumbnail" class="input" placeholder="">
              </div>
              <div class="form-group">
                <div class="section-title">Image URL</div>
                <input type="text" id="image" class="input" placeholder="">
              </div>
            </div>

            <div class="form-row">
              <div class="form-group">
                <div class="section-title">Footer Text</div>
                <input type="text" id="footerText" class="input" placeholder="">
              </div>
              <div class="form-group">
                <div class="section-title">Footer Icon URL</div>
                <input type="text" id="footerIcon" class="input" placeholder="">
              </div>
              <div class="form-group">
                <div class="section-title">Timestamp</div>
                <div class="timestamp-wrapper">
                  <input type="checkbox" id="timestampEnabled" class="custom-checkbox">
                  <label for="timestampEnabled" class="checkbox-label">Enable</label>
                </div>
              </div>
            </div>

            <div class="fields-buttons-container">
              <div class="fields-section">
                <div class="section-with-button">
                  <div class="section-header">Fields</div>
                  <button type="button" id="addField" class="add-button"><span>Add Field</span></button>
                </div>
                <div id="fieldsContainer"></div>
              </div>

              <div class="buttons-section-bottom">
                <div class="section-with-button">
                  <div class="section-header">Buttons</div>
                  <button type="button" id="addButton" class="add-button"><span>Add Button</span></button>
                </div>
                <div id="buttonsContainer"></div>
              </div>
            </div>
          </form>
        </div>

        <!-- Right Panel - Preview -->
        <div class="right-panel">
          <div class="top-buttons">
            <button id="generateBtn" class="top-button generate-button">
              <i class="fas fa-code"></i>
              Generate
            </button>
            <button id="copyBtn" class="top-button copy-button">
              <i class="fas fa-copy"></i>
              Copy
            </button>
            <button id="importBtn" class="top-button import-button">
              <i class="fas fa-file-import"></i>
              Import
            </button>
          </div>
          <div class="preview-area">
            <div class="embed-preview-container">
              <div id="embedPreview" class="embed-preview">
                <div class="discord-message">
                  <div class="message-content" id="previewContent"></div>
                  <div class="embed">
                    <div class="embed-thumbnail" id="previewThumbnail"></div>
                    <div class="embed-author" id="previewAuthor"></div>
                    <div class="embed-title" id="previewTitle"></div>
                    <div class="embed-description" id="previewDescription"></div>
                    <div class="embed-fields" id="previewFields"></div>
                    <div class="embed-image" id="previewImage"></div>
                    <div class="embed-footer" id="previewFooter"></div>
                  </div>
                  <div class="embed-buttons" id="previewButtons"></div>
                </div>
              </div>
            </div>
            <div class="code-output">
              <pre id="codeOutput" class="code-block"></pre>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- Modal for importing embed code -->
  <div id="importModal" class="modal">
    <div class="modal-content glass-card">
      <span class="close-modal">&times;</span>
      <h3>Import Embed Code</h3>
      <textarea id="importCode" placeholder="Paste your embed code here..."></textarea>
      <button id="confirmImport" class="action-button primary">Import</button>
    </div>
  </div>

  <script src="js/ios-detection.js"></script>
  <script src="js/animations.js"></script>
  <script src="js/navigation.js"></script>
  <script src="js/utils.js"></script>
  <script src="js/colorPicker.js"></script>
  <script src="js/fieldManager.js"></script>
  <script src="js/buttonManager.js"></script>
  <script src="js/preview.js"></script>
  <script src="js/embedGenerator.js"></script>
  <script src="js/app.js"></script>
  <script>
    function resetForm() {
      document.getElementById('embedForm').reset();

      // Reset color picker to no color
      const colorInput = document.getElementById('color');
      const colorTextInput = document.getElementById('colorInput');

      if (colorInput) colorInput.value = '#000000';
      if (colorTextInput) colorTextInput.value = '';

      document.getElementById('fieldsContainer').innerHTML = '';
      document.getElementById('buttonsContainer').innerHTML = '';

      // Reset description font size
      if (typeof window.resetDescriptionFontSize === 'function') {
        window.resetDescriptionFontSize();
      } else {
        const description = document.getElementById('description');
        if (description) {
          description.style.fontSize = '';
          description.style.transition = '';
        }
      }

      // Trigger preview update
      if (typeof updatePreview === 'function') {
        updatePreview();
      }
    }

    function openImportModal() {
      const importModal = document.getElementById('importModal');
      if (importModal) {
        importModal.style.display = 'block';
      }
    }
  </script>
</body>
</html>
