<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no, viewport-fit=cover">
  <title>Adore - Commands</title>
  <meta name="description" content="Complete list of Adore bot commands with detailed usage examples and permissions.">
  <link rel="stylesheet" href="css/main.css">
  <link rel="stylesheet" href="css/background.css">
  <link rel="stylesheet" href="css/homepage.css">
  <link rel="stylesheet" href="css/commands.css">
  <link rel="stylesheet" href="css/performance.css">
  <link rel="stylesheet" href="css/no-bounce.css">
  <link rel="stylesheet" href="css/ios-fixes.css">
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
  <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">

</head>
<body class="commands-page">
  <!-- Background -->
  <div class="page-background">
    <div class="stars"></div>
    <div class="twinkling"></div>
    <div class="milky-way"></div>
  </div>

  <!-- Navigation -->
  <nav class="navbar">
    <div class="nav-links">
      <a href="index.html" class="nav-link">Home</a>
      <a href="index.html#features" class="nav-link">Features</a>
      <a href="builder.html" class="nav-link">Builder</a>
      <a href="commands.html" class="nav-link active">Commands</a>
      <a href="https://adore.gitbook.io/adore-docs" class="nav-link" target="_blank">Docs</a>
    </div>
    <div class="mobile-menu-toggle">
      <i class="fas fa-bars"></i>
    </div>
  </nav>

  <!-- Commands Hero Section -->
  <section id="home" class="hero-section">
    <div class="hero-background">
    </div>
    <div class="hero-container">
      <div class="hero-content">

        <h1 class="hero-title animate-on-load delay-200">
          <span class="gradient-text">Adore</span> Commands
        </h1>
        <p class="hero-description animate-on-load delay-300">
          Explore all available commands for Adore bot. From essential utilities to advanced features,
          discover everything you need to enhance your Discord server experience.
        </p>
        <div class="hero-actions animate-on-load delay-400">
          <button class="btn-primary hero-cta" onclick="window.open('https://discord.com/oauth2/authorize?client_id=1378983071650680872', '_blank')">
            <i class="fas fa-plus"></i>
            Invite Adore
          </button>
          <button class="btn-secondary" onclick="document.querySelector('.commands-list').scrollIntoView({behavior: 'smooth'})">
            <i class="fas fa-terminal"></i>
            View Commands
          </button>
        </div>

      </div>



    </div>
  </section>



  <!-- Commands List -->
  <section class="commands-list">
    <div class="commands-container">
      <!-- Liquid Navigation Bar -->
      <div class="liquid-nav-container">
        <div class="liquid-nav">
          <div class="nav-scroll-container">
            <div class="nav-scroll-track">
              <button class="nav-module-btn active" data-module="all">
                <span class="module-name">All Commands</span>
              </button>
              <!-- Module buttons will be populated by JavaScript -->
            </div>
          </div>

          <!-- Animated Search Bar -->
          <div class="search-toggle-container">
            <div class="animated-search">
              <button class="search-icon-btn" id="searchIconBtn">
                <i class="fas fa-search"></i>
              </button>
              <div class="search-input-container" id="searchContainer">
                <input type="text" id="commandSearch" placeholder="Search for your command" class="search-input-field">
                <button class="search-close-btn" id="searchCloseBtn">
                  <i class="fas fa-times"></i>
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Commands Grid -->
      <div class="commands-grid">
        <!-- Commands will be populated by JavaScript -->
      </div>
    </div>
  </section>

  <!-- Copy Notification -->
  <div class="copy-notification" id="copyNotification">
    <i class="fas fa-check"></i>
    <span>Command copied</span>
  </div>



  <!-- Scripts -->
  <script src="js/ios-detection.js"></script>
  <script src="js/navigation.js"></script>
  <script src="js/commands.js"></script>
</body>
</html>
