# Adore Discord Bot - Official Website

A professional website for Adore, a powerful Discord bot with advanced features including embed builder, music capabilities, voice channel management, and platform integrations.

## 🌟 Features

- **Interactive Embed Builder** - Create beautiful Discord embeds with real-time preview
- **Music Features** - High-quality audio with Spotify integration, filters, and queue management
- **Voicemaster** - Personal voice channels with advanced controls
- **Platform Integrations** - Seamless connections with Spotify, Twitter, Instagram, and more
- **Advanced Moderation** - Comprehensive moderation tools and auto-mod features
- **Responsive Design** - Works perfectly on desktop, tablet, and mobile devices

## 🚀 Live Website

Visit the live website: [Adore Discord Bot](https://3206660.github.io/adore-web/)

## 📁 Project Structure

```
adore-web/
├── index.html          # Homepage
├── builder.html        # Embed Builder page
├── commands.html       # Commands documentation
├── docs.html          # Documentation page
├── css/               # Stylesheets
│   ├── main.css       # Main styles
│   ├── homepage.css   # Homepage specific styles
│   ├── builder.css    # Embed builder styles
│   └── ...
├── js/                # JavaScript files
│   ├── app.js         # Main application logic
│   ├── embedGenerator.js # Embed generation
│   ├── preview.js     # Real-time preview
│   └── ...
└── images/            # Assets and images
```

## 🛠️ Technologies Used

- **HTML5** - Semantic markup
- **CSS3** - Modern styling with animations and responsive design
- **JavaScript** - Interactive functionality and real-time features
- **Font Awesome** - Icons
- **Google Fonts** - Typography (Inter font family)
- **AOS (Animate On Scroll)** - Scroll animations

## 🎨 Key Features

### Embed Builder
- Real-time preview of Discord embeds
- Custom color picker
- Field management
- Button and component support
- Export functionality for multiple programming languages

### Music System
- Spotify integration
- Audio filters (Bass Boost, 8D Audio, Nightcore, Vaporwave)
- Queue management
- High-quality audio streaming

### Voicemaster
- Personal voice channels
- Channel controls (lock, invite, limit, rename)
- Easy setup with one command

## 🚀 Getting Started

1. Clone the repository:
   ```bash
   git clone https://github.com/3206660/adore-web.git
   ```

2. Open `index.html` in your browser or serve with a local web server

3. For development, you can use any local server like:
   ```bash
   # Using Python
   python -m http.server 8000

   # Using Node.js (http-server)
   npx http-server

   # Using PHP
   php -S localhost:8000
   ```

## 📱 Responsive Design

The website is fully responsive and optimized for:
- Desktop computers
- Tablets
- Mobile phones
- Various screen sizes and orientations

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🔗 Links

- [Invite Adore Bot](https://discord.com/oauth2/authorize?client_id=1378983071650680872)
- [Discord Server](https://discord.gg/v9tKKAzQ4Q)
- [Documentation](docs.html)

## 📞 Support

For support, join our [Discord server](https://discord.gg/v9tKKAzQ4Q) or open an issue on GitHub.
