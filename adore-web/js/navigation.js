// Navigation functionality
document.addEventListener('DOMContentLoaded', function() {
    // Smooth scrolling for navigation links
    const navLinks = document.querySelectorAll('.nav-link[href^="#"]');
    const heroCtaBtn = document.querySelector('.hero-cta');
    const navCtaBtn = document.querySelector('.nav-cta');
    
    // Handle navigation link clicks
    navLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            const targetId = this.getAttribute('href');
            const targetSection = document.querySelector(targetId);
            
            if (targetSection) {
                const offsetTop = targetSection.offsetTop - 100; // Account for fixed navbar

                // Smooth scroll with custom duration
                const startPosition = window.pageYOffset;
                const distance = offsetTop - startPosition;
                const duration = 1500; // 1.5 seconds for slower animation
                let start = null;

                function animation(currentTime) {
                    if (start === null) start = currentTime;
                    const timeElapsed = currentTime - start;
                    const run = ease(timeElapsed, startPosition, distance, duration);
                    window.scrollTo(0, run);
                    if (timeElapsed < duration) requestAnimationFrame(animation);
                }

                function ease(t, b, c, d) {
                    t /= d / 2;
                    if (t < 1) return c / 2 * t * t + b;
                    t--;
                    return -c / 2 * (t * (t - 2) - 1) + b;
                }

                requestAnimationFrame(animation);

                // Update active nav link immediately
                updateActiveNavLink(this);
            }
        });
    });
    
    // Handle CTA button clicks
    if (heroCtaBtn) {
        heroCtaBtn.addEventListener('click', function() {
            // Navigate to builder page
            window.location.href = 'builder.html';
        });
    }

    if (navCtaBtn) {
        navCtaBtn.addEventListener('click', function() {
            // Check if we're on homepage or builder page
            if (window.location.pathname.includes('builder.html')) {
                window.location.href = 'index.html';
            } else {
                window.location.href = 'builder.html';
            }
        });
    }
    
    // Update active navigation link based on scroll position
    function updateActiveNavLink(activeLink = null) {
        navLinks.forEach(link => link.classList.remove('active'));
        if (activeLink) {
            activeLink.classList.add('active');
        }
    }
    
    // Handle scroll events for active nav highlighting (only on homepage)
    let ticking = false;

    function updateNavOnScroll() {
        // Only run on homepage
        if (window.location.pathname.includes('builder.html')) {
            return;
        }

        const scrollPosition = window.scrollY + 150; // Better threshold
        const sections = ['home', 'features'];

        let currentSection = 'home';

        sections.forEach(sectionId => {
            const section = document.querySelector(`#${sectionId}`);
            if (section && scrollPosition >= section.offsetTop + 100) { // Reduced buffer for better accuracy
                currentSection = sectionId;
            }
        });

        // Update active nav link only if it's different
        const currentActiveLink = document.querySelector('.nav-link.active');
        const newActiveLink = document.querySelector(`.nav-link[href="#${currentSection}"]`);

        if (currentActiveLink !== newActiveLink) {
            navLinks.forEach(link => link.classList.remove('active'));
            if (newActiveLink) {
                newActiveLink.classList.add('active');
            }
        }

        ticking = false;
    }
    
    function requestTick() {
        if (!ticking) {
            requestAnimationFrame(updateNavOnScroll);
            ticking = true;
        }
    }
    
    window.addEventListener('scroll', requestTick);
    
    // Mobile menu toggle (for future implementation)
    const mobileMenuToggle = document.querySelector('.mobile-menu-toggle');
    const navLinksContainer = document.querySelector('.nav-links');
    
    if (mobileMenuToggle && navLinksContainer) {
        mobileMenuToggle.addEventListener('click', function() {
            navLinksContainer.classList.toggle('mobile-open');
            this.classList.toggle('active');
        });
    }
    
    // Epic Navbar effects on scroll with hide/show behavior
    const navbar = document.querySelector('.navbar');
    let lastScrollTop = 0;
    let scrollDirection = 'up';

    function updateNavbarBackground() {
        const currentScroll = window.scrollY;
        const scrolled = currentScroll > 50;

        // Determine scroll direction
        if (currentScroll > lastScrollTop && currentScroll > 100) {
            // Scrolling down
            scrollDirection = 'down';
            navbar.classList.add('hidden');
        } else {
            // Scrolling up
            scrollDirection = 'up';
            navbar.classList.remove('hidden');
        }

        // Add scrolled class for background
        if (scrolled) {
            navbar.classList.add('scrolled');
        } else {
            navbar.classList.remove('scrolled');
        }

        lastScrollTop = currentScroll;
    }

    window.addEventListener('scroll', updateNavbarBackground);

    // Epic brand icon rotation on hover
    const brandIcon = document.querySelector('.brand-icon');
    if (brandIcon) {
        brandIcon.addEventListener('mouseenter', function() {
            this.style.transform = 'rotate(360deg) scale(1.1)';
        });

        brandIcon.addEventListener('mouseleave', function() {
            this.style.transform = 'rotate(0deg) scale(1)';
        });
    }

    // Add click ripple effect to nav links
    const navLinks = document.querySelectorAll('.nav-link, .nav-cta');
    navLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            const ripple = document.createElement('span');
            const rect = this.getBoundingClientRect();
            const size = Math.max(rect.width, rect.height);
            const x = e.clientX - rect.left - size / 2;
            const y = e.clientY - rect.top - size / 2;

            ripple.style.width = ripple.style.height = size + 'px';
            ripple.style.left = x + 'px';
            ripple.style.top = y + 'px';
            ripple.classList.add('ripple');

            this.appendChild(ripple);

            setTimeout(() => {
                ripple.remove();
            }, 600);
        });
    });
    
    // Add some interactive animations
    const featureCards = document.querySelectorAll('.feature-card');
    
    // Intersection Observer for feature cards animation
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };
    
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.style.opacity = '1';
                entry.target.style.transform = 'translateY(0)';
            }
        });
    }, observerOptions);
    
    featureCards.forEach(card => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(30px)';
        card.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
        observer.observe(card);
    });
    
    // Add floating animation to hero visual
    const heroVisual = document.querySelector('.hero-visual');
    if (heroVisual) {
        let mouseX = 0;
        let mouseY = 0;
        let targetX = 0;
        let targetY = 0;
        
        document.addEventListener('mousemove', (e) => {
            mouseX = (e.clientX - window.innerWidth / 2) * 0.01;
            mouseY = (e.clientY - window.innerHeight / 2) * 0.01;
        });
        
        function animateHeroVisual() {
            targetX += (mouseX - targetX) * 0.1;
            targetY += (mouseY - targetY) * 0.1;
            
            heroVisual.style.transform = `translate(${targetX}px, ${targetY}px)`;
            requestAnimationFrame(animateHeroVisual);
        }
        
        animateHeroVisual();
    }
});
