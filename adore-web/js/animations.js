/**
 * Efficient Animation System
 * Replaces AOS with optimized Intersection Observer-based animations
 */

// Animation classes and styles
const animationStyles = `
  .animate-on-load {
    opacity: 0;
    transform: translateY(30px);
    transition: opacity 0.6s ease, transform 0.6s ease;
  }

  .animate-on-load.delay-100 { transition-delay: 0.1s; }
  .animate-on-load.delay-200 { transition-delay: 0.2s; }
  .animate-on-load.delay-300 { transition-delay: 0.3s; }
  .animate-on-load.delay-400 { transition-delay: 0.4s; }
  .animate-on-load.delay-500 { transition-delay: 0.5s; }
  .animate-on-load.delay-600 { transition-delay: 0.6s; }

  .animate-on-scroll {
    opacity: 0;
    transform: translateY(50px);
    transition: opacity 0.8s ease, transform 0.8s ease;
  }

  .animate-on-scroll.delay-100 { transition-delay: 0.1s; }
  .animate-on-scroll.delay-200 { transition-delay: 0.2s; }
  .animate-on-scroll.delay-300 { transition-delay: 0.3s; }
  .animate-on-scroll.delay-400 { transition-delay: 0.4s; }
  .animate-on-scroll.delay-500 { transition-delay: 0.5s; }

  .animate-on-load.loaded,
  .animate-on-scroll.visible {
    opacity: 1;
    transform: translateY(0);
  }

  /* Reduce motion for users who prefer it */
  @media (prefers-reduced-motion: reduce) {
    .animate-on-load,
    .animate-on-scroll {
      transition: none;
      opacity: 1;
      transform: none;
    }
  }
`;

// Inject styles
function injectStyles() {
  const styleSheet = document.createElement('style');
  styleSheet.textContent = animationStyles;
  document.head.appendChild(styleSheet);
}

// Optimized Intersection Observer
let observer;

function createObserver() {
  const options = {
    threshold: 0.1,
    rootMargin: '0px 0px -50px 0px'
  };

  observer = new IntersectionObserver((entries) => {
    entries.forEach(entry => {
      if (entry.isIntersecting) {
        entry.target.classList.add('visible');
        // Stop observing once animated to prevent re-triggering
        observer.unobserve(entry.target);
      }
    });
  }, options);
}

// Initialize animations
function initAnimations() {
  // Inject CSS
  injectStyles();
  
  // Create observer
  createObserver();
  
  // Animate elements on load (hero section)
  const loadElements = document.querySelectorAll('.animate-on-load');
  setTimeout(() => {
    loadElements.forEach(el => {
      el.classList.add('loaded');
    });
  }, 100);
  
  // Observe scroll elements
  const scrollElements = document.querySelectorAll('.animate-on-scroll');
  scrollElements.forEach(el => {
    observer.observe(el);
  });
}

// Performance optimizations
function optimizePerformance() {
  // Disable animations on low-end devices
  if (navigator.hardwareConcurrency && navigator.hardwareConcurrency < 4) {
    document.documentElement.style.setProperty('--animation-duration', '0.3s');
  }
  
  // Reduce animations on mobile
  if (window.innerWidth < 768) {
    document.documentElement.style.setProperty('--animation-duration', '0.4s');
  }
}

// Ensure scrolling animations are always running smoothly
function ensureScrollingAnimations() {
  const serversTrack = document.querySelector('.servers-track');
  const integrationsTrack = document.querySelector('.integrations-track');

  if (serversTrack) {
    serversTrack.style.animationPlayState = 'running';
    serversTrack.style.animationTimingFunction = 'linear';
    serversTrack.style.animationDuration = '40s';
  }

  if (integrationsTrack) {
    integrationsTrack.style.animationPlayState = 'running';
    integrationsTrack.style.animationTimingFunction = 'linear';
    integrationsTrack.style.animationDuration = '40s';
  }
}

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
  optimizePerformance();
  initAnimations();

  // Ensure scrolling animations are running
  ensureScrollingAnimations();

  // Check and fix scrolling animations periodically
  setInterval(ensureScrollingAnimations, 5000);
});

// Handle page visibility changes to pause only specific animations when tab is not active
document.addEventListener('visibilitychange', () => {
  // Only pause non-essential animations, not scrolling tracks
  const pausableElements = document.querySelectorAll('.cosmic-rings, .feature-orbs, .cosmic-particles, .energy-waves, .hero-particles');

  if (document.hidden) {
    // Pause only decorative animations when tab is hidden
    pausableElements.forEach(el => {
      el.style.animationPlayState = 'paused';
    });
  } else {
    // Resume decorative animations when tab is visible
    pausableElements.forEach(el => {
      el.style.animationPlayState = 'running';
    });
    // Ensure scrolling animations are still running
    ensureScrollingAnimations();
  }
});

// Handle window focus/blur to maintain scrolling animations
window.addEventListener('blur', () => {
  // Keep scrolling animations running even when window loses focus
  setTimeout(ensureScrollingAnimations, 100);
});

window.addEventListener('focus', () => {
  // Ensure scrolling animations are running when window gains focus
  ensureScrollingAnimations();
});

// Cleanup on page unload
window.addEventListener('beforeunload', () => {
  if (observer) {
    observer.disconnect();
  }
});
