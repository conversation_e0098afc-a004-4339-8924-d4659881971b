// Commands data - loaded from JSON file
let commandsData = null;

// Load commands data from JSON file
async function loadCommandsData() {
  try {
    const response = await fetch('./js/commands.json');
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    commandsData = await response.json();
    return commandsData;
  } catch (error) {
    console.error('Error loading commands data:', error);
    // Fallback data in case JSON fails to load
    commandsData = [
      {
        "name": "help",
        "aliases": ["h"],
        "description": "Shows detailed information about commands",
        "usage": "{prefix}help [command]",
        "permission": null,
        "module": "Essentials"
      },
      {
        "name": "ping",
        "aliases": ["latency"],
        "description": "Check bot latency and response time",
        "usage": "{prefix}ping",
        "permission": null,
        "module": "Essentials"
      }
    ];
    return commandsData;
  }
}

// Global variables
let allCommands = [];
let filteredCommands = [];
let currentModule = 'all';

// Get icon for module
function getModuleIcon(module) {
  const icons = {
    'Essentials': 'fas fa-cog',
    'Information': 'fas fa-info-circle',
    'Lastfm': 'fas fa-music',
    'Miscellaneous': 'fas fa-puzzle-piece',
    'Moderation': 'fas fa-shield-alt',
    'Premium': 'fas fa-star',
    'Purge': 'fas fa-trash',
    'Server': 'fas fa-server',
    'Service': 'fas fa-tools',
    'Snipes': 'fas fa-eye',
    'Socials': 'fas fa-share-alt',
    'Utility': 'fas fa-wrench',
    'Gamble': 'fas fa-dice'
  };
  return icons[module] || 'fas fa-code';
}



// Initialize the commands page
document.addEventListener('DOMContentLoaded', async function() {
  console.log('DOM loaded, initializing commands page...');
  try {
    console.log('Loading commands data...');
    await loadCommandsData();
    console.log('Commands data loaded:', commandsData);

    if (commandsData && Array.isArray(commandsData)) {
      allCommands = commandsData;
      filteredCommands = [...allCommands];
      console.log('All commands:', allCommands.length);

      renderModuleFilters();
      renderCommands();
      setupSearch();
      console.log('Commands page initialized successfully');
    } else {
      console.error('No commands data available');
      throw new Error('No commands data available');
    }
  } catch (error) {
    console.error('Failed to initialize commands page:', error);
    // Show error message to user
    const commandsGrid = document.querySelector('.commands-grid');
    if (commandsGrid) {
      commandsGrid.innerHTML = `
        <div class="no-commands">
          <i class="fas fa-exclamation-triangle"></i>
          <h3>Failed to load commands</h3>
          <p>Please refresh the page to try again</p>
          <p style="font-size: 0.8rem; color: #666;">Error: ${error.message}</p>
        </div>
      `;
    }
  }
});

// Get unique modules from commands (excluding Dev module)
function getUniqueModules() {
  const modules = [...new Set(allCommands.map(cmd => {
    // Extract main module name (before any " / " separator)
    const mainModule = cmd.module.split(' / ')[0];
    return mainModule;
  }))];

  // Filter out Dev module and sort
  return modules.filter(module => module !== 'Dev').sort();
}

// Render module filter buttons
function renderModuleFilters() {
  const navScrollTrack = document.querySelector('.nav-scroll-track');
  if (!navScrollTrack) return;

  const modules = getUniqueModules();
  const moduleButtons = modules.map(module => {
    const commandCount = allCommands.filter(cmd => cmd.module.startsWith(module)).length;

    return `
      <button class="nav-module-btn" data-module="${module}">
        <span class="module-name">${module}</span>
        <span class="module-count">${commandCount}</span>
      </button>
    `;
  }).join('');

  // Find the "All Commands" button and add module buttons after it
  const allButton = navScrollTrack.querySelector('[data-module="all"]');
  allButton.insertAdjacentHTML('afterend', moduleButtons);

  // Add click handlers
  navScrollTrack.addEventListener('click', (e) => {
    const btn = e.target.closest('.nav-module-btn');
    if (!btn) return;

    const module = btn.dataset.module;
    filterByModule(module);

    // Update active state
    navScrollTrack.querySelectorAll('.nav-module-btn').forEach(b => b.classList.remove('active'));
    btn.classList.add('active');
  });
}

// Filter commands by module
function filterByModule(module) {
  currentModule = module;
  if (module === 'all') {
    filteredCommands = [...allCommands];
  } else {
    filteredCommands = allCommands.filter(cmd => cmd.module.startsWith(module));
  }

  // Apply search filter if there's a search term
  const searchTerm = document.getElementById('commandSearch').value.trim();
  if (searchTerm) {
    applySearchFilter(searchTerm);
  }

  renderCommands();
}

// Setup search functionality
function setupSearch() {
  const searchIconBtn = document.getElementById('searchIconBtn');
  const searchContainer = document.getElementById('searchContainer');
  const searchCloseBtn = document.getElementById('searchCloseBtn');
  const searchInput = document.getElementById('commandSearch');

  if (!searchIconBtn || !searchContainer || !searchCloseBtn || !searchInput) {
    console.error('Search elements not found');
    return;
  }

  // Expand search bar
  function expandSearch() {
    searchContainer.classList.add('expanded');
    setTimeout(() => {
      searchInput.focus();
    }, 200);
  }

  // Collapse search bar
  function collapseSearch() {
    searchContainer.classList.remove('expanded');
    searchInput.value = '';
    searchInput.blur();
    // Reset to show all commands
    filterByModule(currentModule);
  }

  // Event listeners
  searchIconBtn.addEventListener('click', expandSearch);
  searchCloseBtn.addEventListener('click', collapseSearch);

  // Close on escape key
  document.addEventListener('keydown', (e) => {
    if (e.key === 'Escape' && searchContainer.classList.contains('expanded')) {
      collapseSearch();
    }
  });

  // Close when clicking outside
  document.addEventListener('click', (e) => {
    if (!searchContainer.contains(e.target) && !searchIconBtn.contains(e.target) && searchContainer.classList.contains('expanded')) {
      collapseSearch();
    }
  });

  // Search input handler
  searchInput.addEventListener('input', (e) => {
    const searchTerm = e.target.value.trim();
    if (searchTerm) {
      applySearchFilter(searchTerm);
    } else {
      // Reset to current module filter
      filterByModule(currentModule);
    }
  });

  // Prevent search input from losing focus when clicking inside container
  searchInput.addEventListener('blur', () => {
    // Small delay to allow click events to process
    setTimeout(() => {
      if (!searchContainer.contains(document.activeElement) && searchInput.value === '') {
        collapseSearch();
      }
    }, 100);
  });
}

// Apply search filter
function applySearchFilter(searchTerm) {
  const term = searchTerm.toLowerCase();

  // Start with current module filter
  let baseCommands = currentModule === 'all' ? allCommands : allCommands.filter(cmd => cmd.module.startsWith(currentModule));

  filteredCommands = baseCommands.filter(cmd => {
    return cmd.name.toLowerCase().includes(term) ||
           cmd.description.toLowerCase().includes(term) ||
           cmd.module.toLowerCase().includes(term) ||
           cmd.aliases.some(alias => alias.toLowerCase().includes(term));
  });

  renderCommands();
}

// Render commands
function renderCommands() {
  const commandsGrid = document.querySelector('.commands-grid');
  if (!commandsGrid) return;

  if (filteredCommands.length === 0) {
    commandsGrid.innerHTML = `
      <div class="no-commands">
        <i class="fas fa-search"></i>
        <h3>No commands found</h3>
        <p>Try adjusting your search or filter criteria</p>
      </div>
    `;
    return;
  }

  commandsGrid.innerHTML = filteredCommands.map(command => {
    const prefix = ','; // Default prefix
    const aliases = command.aliases && command.aliases.length > 0 ? command.aliases : [];
    const permission = command.permission || 'None';

    // Show only the first alias
    const firstAlias = aliases.length > 0 ? aliases[0] : null;

    return `
      <div class="command-card" data-module="${command.module}">
        <div class="command-header">
          <h3 class="command-name">${command.name}</h3>
          <button class="copy-btn" onclick="copyCommandWithNotification('${prefix}${command.name}')" title="Copy command">
            <i class="fas fa-copy"></i>
          </button>
        </div>

        <p class="command-description">${command.description}</p>

        <div class="command-details">
          <div class="command-detail">
            <span class="detail-label">arguments</span>
            <div class="detail-value">
              ${firstAlias ?
                `<span class="alias-tag">${firstAlias}</span>` :
                '<span class="no-aliases">none</span>'
              }
            </div>
          </div>
          <div class="command-detail">
            <span class="detail-label">permissions</span>
            <div class="detail-value">
              <span class="permission-tag">${permission.toLowerCase()}</span>
            </div>
          </div>
        </div>
      </div>
    `;
  }).join('');

  // Animate cards
  const cards = document.querySelectorAll('.command-card');
  cards.forEach((card, index) => {
    card.style.opacity = '0';
    card.style.transform = 'translateY(20px)';

    setTimeout(() => {
      card.style.transition = 'all 0.3s ease';
      card.style.opacity = '1';
      card.style.transform = 'translateY(0)';
    }, index * 30);
  });
}

// Copy command function with notification
function copyCommandWithNotification(command) {
  navigator.clipboard.writeText(command).then(() => {
    showCopyNotification();
  }).catch(err => {
    console.error('Failed to copy command:', err);
  });
}

// Show copy notification
function showCopyNotification() {
  const notification = document.getElementById('copyNotification');
  if (!notification) return;

  notification.classList.add('show');

  setTimeout(() => {
    notification.classList.remove('show');
  }, 2000);
}

// Make copyCommand available globally for backward compatibility
window.copyCommand = copyCommandWithNotification;
window.copyCommandWithNotification = copyCommandWithNotification;
