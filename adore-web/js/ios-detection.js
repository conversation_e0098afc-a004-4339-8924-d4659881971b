/**
 * iOS Detection and Fixes
 * Detects iOS devices and applies necessary fixes for proper rendering
 */

(function() {
  'use strict';

  // Enhanced iOS detection
  function isIOSDevice() {
    return [
      'iPad Simulator',
      'iPhone Simulator',
      'iPod Simulator',
      'iPad',
      'iPhone',
      'iPod'
    ].includes(navigator.platform)
    // iPad on iOS 13 detection
    || (navigator.userAgent.includes("Mac") && "ontouchend" in document);
  }

  function isIOSSafari() {
    const iOS = isIOSDevice();
    const safari = /Safari/.test(navigator.userAgent) && !/Chrome/.test(navigator.userAgent);
    return iOS || safari;
  }

  // Apply iOS-specific fixes
  function applyIOSFixes() {
    if (!isIOSDevice()) {
      console.log('Not iOS device, skipping iOS fixes');
      return;
    }

    console.log('iOS device detected, applying fixes...');

    // Add iOS class to body for CSS targeting
    document.body.classList.add('ios-device');
    
    // Force background rendering
    const body = document.body;
    const pageBackground = document.querySelector('.page-background');
    
    if (pageBackground) {
      // Ensure background is properly positioned
      pageBackground.style.position = 'fixed';
      pageBackground.style.top = '0';
      pageBackground.style.left = '0';
      pageBackground.style.width = '100%';
      pageBackground.style.height = '100%';
      pageBackground.style.zIndex = '1';
      pageBackground.style.pointerEvents = 'none';
    }

    // Fix star backgrounds
    const stars = document.querySelector('.stars');
    const twinkling = document.querySelector('.twinkling');
    const milkyWay = document.querySelector('.milky-way');

    if (stars) {
      stars.style.position = 'fixed';
      stars.style.top = '0';
      stars.style.left = '0';
      stars.style.width = '100%';
      stars.style.height = '100%';
      stars.style.zIndex = '2';
      stars.style.opacity = '0.6';
    }

    if (twinkling) {
      twinkling.style.position = 'fixed';
      twinkling.style.top = '0';
      twinkling.style.left = '0';
      twinkling.style.width = '100%';
      twinkling.style.height = '100%';
      twinkling.style.zIndex = '3';
      twinkling.style.opacity = '0.5';
    }

    if (milkyWay) {
      milkyWay.style.position = 'fixed';
      milkyWay.style.top = '0';
      milkyWay.style.left = '0';
      milkyWay.style.width = '100%';
      milkyWay.style.height = '100%';
      milkyWay.style.zIndex = '4';
      milkyWay.style.opacity = '0.7';
    }

    // Ensure content is above background
    const contentElements = document.querySelectorAll('nav, main, section, footer, .hero-section, .commands-list, .builder-container');
    contentElements.forEach(element => {
      element.style.position = 'relative';
      element.style.zIndex = '10';
    });

    // Force CSS variables to work
    const root = document.documentElement;
    root.style.setProperty('--background', '#0a0a0a');
    root.style.setProperty('--background-secondary', '#111111');
    root.style.setProperty('--text-primary', '#ffffff');
    root.style.setProperty('--text-secondary', '#ffffff');

    // Apply fallback background if gradient fails
    if (getComputedStyle(body).backgroundColor === 'rgba(0, 0, 0, 0)' || 
        getComputedStyle(body).backgroundColor === 'transparent') {
      body.style.backgroundColor = '#0a0a0a';
      console.log('Applied fallback background color');
    }

    // Fix viewport height for iOS Safari
    const setVH = () => {
      const vh = window.innerHeight * 0.01;
      root.style.setProperty('--vh', `${vh}px`);
    };
    
    setVH();
    window.addEventListener('resize', setVH);
    window.addEventListener('orientationchange', () => {
      setTimeout(setVH, 100);
    });

    console.log('iOS fixes applied successfully');
  }

  // Apply fixes when DOM is ready
  function initIOSFixes() {
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', applyIOSFixes);
    } else {
      applyIOSFixes();
    }
  }

  // Initialize immediately
  initIOSFixes();

  // Re-apply fixes on page visibility change (iOS Safari tab switching)
  document.addEventListener('visibilitychange', function() {
    if (!document.hidden) {
      setTimeout(applyIOSFixes, 100);
    }
  });

  // Export for manual use if needed
  window.applyIOSFixes = applyIOSFixes;
  window.isIOSDevice = isIOSDevice;

})();
