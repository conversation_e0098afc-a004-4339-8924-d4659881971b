/**
 * Servers Display System
 * Loads and displays server data from servers.json
 */

let serversData = [];

// Format member count for display
function formatMemberCount(count) {
  if (count >= 1000000) {
    return (count / 1000000).toFixed(1).replace('.0', '') + 'M+ members';
  } else if (count >= 1000) {
    return (count / 1000).toFixed(1).replace('.0', '') + 'K+ members';
  } else {
    return count + ' members';
  }
}

// Create server card HTML
function createServerCard(server) {
  return `
    <div class="server-card">
      <img src="${server.server_icon}" alt="${server.server_name}" class="server-icon">
      <div class="server-info">
        <div class="server-name">${server.server_name}</div>
        <div class="server-members">${formatMemberCount(server.server_members)}</div>
      </div>
    </div>
  `;
}

// Load servers data from JSON file
async function loadServersData() {
  try {
    const response = await fetch('./js/servers.json');
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    serversData = await response.json();
    return serversData;
  } catch (error) {
    console.error('Error loading servers data:', error);
    // Fallback data in case JSON fails to load
    serversData = [
      {
        "server_icon": "https://cdn.discordapp.com/icons/110373943822540800/a_63d8cd4d8e1c5b2b3b0b8f8f8f8f8f8f.webp",
        "server_name": "Discord API",
        "server_members": 850000
      },
      {
        "server_icon": "https://cdn.discordapp.com/icons/267624335836053506/a_9f6f9f6f9f6f9f6f9f6f9f6f9f6f9f6f.webp",
        "server_name": "Python",
        "server_members": 450000
      }
    ];
    return serversData;
  }
}

// Render servers to the DOM
function renderServers() {
  const serversTrack = document.getElementById('servers-track');
  if (!serversTrack) {
    console.error('Servers track element not found');
    return;
  }

  if (!serversData || serversData.length === 0) {
    console.error('No servers data available');
    return;
  }

  // Create server cards HTML
  let serversHTML = '';
  
  // Add all servers
  serversData.forEach(server => {
    serversHTML += createServerCard(server);
  });
  
  // Duplicate servers for seamless loop animation
  serversData.forEach(server => {
    serversHTML += createServerCard(server);
  });

  // Insert the HTML
  serversTrack.innerHTML = serversHTML;
  
  console.log(`Rendered ${serversData.length} servers (duplicated for animation)`);
}

// Initialize servers display
async function initServers() {
  try {
    console.log('Initializing servers display...');
    await loadServersData();
    console.log('Servers data loaded:', serversData.length, 'servers');

    if (serversData && Array.isArray(serversData) && serversData.length > 0) {
      renderServers();
      console.log('Servers display initialized successfully');
    } else {
      throw new Error('Invalid servers data format or empty array');
    }
  } catch (error) {
    console.error('Failed to initialize servers display:', error);
    // Show error message or fallback content
    const serversTrack = document.getElementById('servers-track');
    if (serversTrack) {
      serversTrack.innerHTML = `
        <div class="server-card">
          <div class="server-info">
            <div class="server-name">Failed to load servers</div>
            <div class="server-members">Please refresh</div>
          </div>
        </div>
      `;
    }
  }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
  initServers();
  initMobileTouchScrolling();
});

// Initialize mobile touch scrolling for server list
function initMobileTouchScrolling() {
  const serversScrollContainer = document.querySelector('.servers-scroll-container');

  if (!serversScrollContainer) return;

  // Check if device is mobile
  const isMobile = window.innerWidth <= 768;

  if (isMobile) {
    // Enable touch scrolling on mobile
    serversScrollContainer.style.overflowX = 'auto';
    serversScrollContainer.style.webkitOverflowScrolling = 'touch';

    // Disable animation on mobile for better touch experience
    const serversTrack = serversScrollContainer.querySelector('.servers-track');
    if (serversTrack) {
      serversTrack.style.animation = 'none';
      serversTrack.style.width = 'max-content';
    }

    // Add touch event listeners for better mobile experience
    let isScrolling = false;

    serversScrollContainer.addEventListener('touchstart', () => {
      isScrolling = true;
    }, { passive: true });

    serversScrollContainer.addEventListener('touchend', () => {
      setTimeout(() => {
        isScrolling = false;
      }, 100);
    }, { passive: true });

    // Prevent default touch behaviors that might interfere
    serversScrollContainer.addEventListener('touchmove', (e) => {
      if (isScrolling) {
        e.stopPropagation();
      }
    }, { passive: true });
  }
}

// Re-initialize on window resize
window.addEventListener('resize', () => {
  initMobileTouchScrolling();
});

// Export functions for potential external use
if (typeof module !== 'undefined' && module.exports) {
  module.exports = {
    loadServersData,
    renderServers,
    formatMemberCount,
    initServers
  };
}
