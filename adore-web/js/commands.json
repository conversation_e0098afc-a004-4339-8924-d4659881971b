[{"name": "blacklist", "aliases": ["bl", "globalban"], "description": "Universal blacklist system for users and servers", "usage": "{guildprefix}blacklist users\\n{guildprefix}blacklist users {user.id} {reason}\\n\\n{guildprefix}blacklist servers\\n{guildprefix}blacklist servers {server.id} {reason}", "permission": "dev", "module": "<PERSON>"}, {"name": "reload", "aliases": [], "description": "Reloads all commands", "usage": "{guildprefix}reload", "permission": null, "module": "<PERSON>"}, {"name": "dcmd", "aliases": ["disable", "disablecmd"], "description": "Disable commands or categories", "usage": "{guildprefix}dcmd [command/category/path]\\n{guildprefix}dcmd list", "permission": null, "module": "Dev / Cmdcore"}, {"name": "ecmd", "aliases": ["enable", "enablecmd"], "description": "Enable commands or categories", "usage": "{guildprefix}ecmd [command/category/path]\\n{guildprefix}ecmd list", "permission": null, "module": "Dev / Cmdcore"}, {"name": "copyembed", "aliases": ["copy"], "description": "copy an embed custom syntax code", "usage": "{guildprefix}copyembed [message id or message link]", "permission": "Manage Messages", "module": "Essentials"}, {"name": "<PERSON><PERSON>bed", "aliases": ["ce"], "description": "Create your own embed", "usage": "{guildprefix}createembed [embed code]\\n{guildprefix}createembed {title: Hello {user}}$v{description: Welcome to {guild.name}}", "permission": "Manage Messages", "module": "Essentials"}, {"name": "help", "aliases": ["h"], "description": "Shows more information about a command", "usage": "{guildprefix}help\\n{guildprefix}help [command]", "permission": null, "module": "Essentials"}, {"name": "latency", "aliases": ["ms", "ping"], "description": "Shows bot latency and API response times", "usage": "{guildprefix}latency", "permission": null, "module": "Essentials"}, {"name": "prefix", "aliases": ["setprefix"], "description": "Manage server prefix", "usage": "{guildprefix}prefix [set] [prefix]", "permission": null, "module": "Essentials"}, {"name": "account", "aliases": ["create", "open"], "description": "Create a gambling account to start using gambling commands", "usage": "{guildprefix}account", "permission": null, "module": "Gamble"}, {"name": "bal", "aliases": ["balance", "money"], "description": "Check your or someone else wallet and bank balance", "usage": "{guildprefix}bal\\n{guildprefix}bal [user]", "permission": null, "module": "Gamble"}, {"name": "beg", "aliases": ["plead"], "description": "Beg for coins with a cooldown", "usage": "{guildprefix}beg", "permission": null, "module": "Gamble"}, {"name": "coinflip", "aliases": ["cf", "flip"], "description": "Flip a coin and bet on heads or tails", "usage": "{guildprefix}coinflip [amount] [heads/tails]\\n{guildprefix}cf 1000 heads\\n{guildprefix}cf all tails", "permission": null, "module": "Gamble"}, {"name": "crash", "aliases": ["cr"], "description": "Play crash game - cash out before it crashes!", "usage": "{guildprefix}crash [amount]\\n{guildprefix}crash 1000\\n{guildprefix}crash all", "permission": null, "module": "Gamble"}, {"name": "daily", "aliases": ["claim"], "description": "Claim your daily coins with streak bonuses", "usage": "{guildprefix}daily", "permission": null, "module": "Gamble"}, {"name": "deposit", "aliases": ["dep"], "description": "Deposit money from your wallet to your bank", "usage": "{guildprefix}deposit [amount]\\n{guildprefix}deposit 1000\\n{guildprefix}deposit all\\n{guildprefix}deposit 50%", "permission": null, "module": "Gamble"}, {"name": "leaderboard", "aliases": ["lb", "top", "rich"], "description": "View the richest users globally", "usage": "{guildprefix}leaderboard", "permission": null, "module": "Gamble"}, {"name": "rob", "aliases": ["steal"], "description": "Attempt to rob money from another user (30% success rate, 1 hour cooldown)", "usage": "{guildprefix}rob [user]\\n{guildprefix}rob @user", "permission": null, "module": "Gamble"}, {"name": "transfer", "aliases": ["give", "pay"], "description": "Transfer money from your wallet to another user", "usage": "{guildprefix}transfer [user] [amount]\\n{guildprefix}transfer @user 1000\\n{guildprefix}transfer @user all", "permission": null, "module": "Gamble"}, {"name": "withdraw", "aliases": ["with"], "description": "Withdraw money from your bank to your wallet", "usage": "{guildprefix}withdraw [amount]\\n{guildprefix}withdraw 1000\\n{guildprefix}withdraw all\\n{guildprefix}withdraw 50%", "permission": null, "module": "Gamble"}, {"name": "afk", "aliases": [], "description": "Set an AFK status for when you are mentioned", "usage": "{guildprefix}afk [reason]", "permission": null, "module": "Information"}, {"name": "timezone", "aliases": ["tz"], "description": "View or set timezone information", "usage": "{guildprefix}tz\\n{guildprefix}tz [user]\\n{guildprefix}tz set [city]", "permission": null, "module": "Information"}, {"name": "userinfo", "aliases": ["lookup", "whois", "ui"], "description": "Get information about a Discord user", "usage": "{guildprefix}userinfo\\n{guildprefix}userinfo [user]", "permission": null, "module": "Information"}, {"name": "fm", "aliases": ["np"], "description": "Show your current or last played track from Last.fm", "usage": "{guildprefix}fm\\n{guildprefix}fm [user]", "permission": null, "module": "Lastfm"}, {"name": "lastfm", "aliases": ["lf"], "description": "Manage your last.fm profile and settings", "usage": "{guildprefix}lastfm howto\\n{guildprefix}lastfm set [lastfm username]\\n{guildprefix}lastfm wk [artist]\\n{guildprefix}lastfm ta [user] [period]", "permission": null, "module": "Lastfm"}, {"name": "8ball", "aliases": ["8b", "ask"], "description": "ask the magic 8-ball a question", "usage": "{guildprefix}8ball [question]", "permission": null, "module": "Miscellaneous"}, {"name": "advice", "aliases": [], "description": "sends a random piece of advice", "usage": "{guildprefix}advice", "permission": null, "module": "Miscellaneous"}, {"name": "img", "aliases": ["image", "search"], "description": "Search for images using Google Custom Search", "usage": "{guildprefix}img [search query]", "permission": null, "module": "Miscellaneous"}, {"name": "say", "aliases": [], "description": "Say a message", "usage": "{guildprefix}say [message]", "permission": "Administrator", "module": "Miscellaneous"}, {"name": "ttt", "aliases": ["tict<PERSON><PERSON>", "tic"], "description": "Play tic tac toe with another user or against AI", "usage": "{guildprefix}ttt [@user] - Play with a user\\n{guildprefix}ttt - Play with AI\\n{guildprefix}ttt stats - View your statistics", "permission": null, "module": "Miscellaneous"}, {"name": "yo<PERSON><PERSON>", "aliases": ["ym", "yomomma"], "description": "sends a random yo mama joke", "usage": "{guildprefix}yomama", "permission": null, "module": "Miscellaneous"}, {"name": "youtube", "aliases": ["yt"], "description": "search for a song/video on youtube", "usage": "{guildprefix}youtube [song/video]", "permission": null, "module": "Miscellaneous"}, {"name": "automod", "aliases": ["filter"], "description": "Manage Discord", "usage": "{guildprefix}automod words add [word] - add word to filter\\n{guildprefix}automod words remove [word] - remove word from filter\\n{guildprefix}automod words bypass [role/user] - toggle role/user bypass for word filter\\n{guildprefix}automod words list - show word filter settings\\n{guildprefix}automod invites on/off - toggle invite filtering\\n{guildprefix}automod invites bypass [role/user] - toggle role/user bypass for invite filter\\n{guildprefix}automod invites list - show invite filter settings\\n{guildprefix}automod bypass - show all bypass roles and users\\n{guildprefix}automod list - show all filter status\\n{guildprefix}automod reset - reset all filters", "permission": "Manage Guild", "module": "Moderation"}, {"name": "ban", "aliases": ["deport", "hackban", "hban", "hb"], "description": "ban the mentioned user from the server (works with user IDs for users not in server)", "usage": "{guildprefix}ban [user]\\n{guildprefix}ban [user] [reason]\\n{guildprefix}ban [userID] [reason] (hackban)", "permission": "Ban Members", "module": "Moderation / Ban"}, {"name": "unban", "aliases": [], "description": "unban the mentioned user from the server", "usage": "{guildprefix}unban [user]\\n{guildprefix}unban [user] [reason]", "permission": "Ban Members", "module": "Moderation / Ban"}, {"name": "history", "aliases": [], "description": "view and manage moderation history", "usage": "{guildprefix}history [user]\\n{guildprefix}history [user] [action]\\n{guildprefix}history view [caseid]\\n{guildprefix}history remove [user] [caseid]\\n{guildprefix}history removeall [user]", "permission": "Manage Messages", "module": "Moderation / History"}, {"name": "jail", "aliases": ["jailed"], "description": "jails the specified member or lists jailed users", "usage": "{guildprefix}jail [user]\\n{guildprefix}jail [user] [reason]\\n{guildprefix}jail list", "permission": "Manage Roles", "module": "Moderation / Jail"}, {"name": "jailsetup", "aliases": ["setup"], "description": "automatically setup jail role and channel with proper permissions", "usage": "{guildprefix}jailsetup", "permission": "Administrator", "module": "Moderation / Jail"}, {"name": "unjail", "aliases": [], "description": "unjails the specified member", "usage": "{guildprefix}unjail [user]\\n{guildprefix}unjail [user] [reason]", "permission": "Manage Roles", "module": "Moderation / Jail"}, {"name": "kick", "aliases": ["boot"], "description": "Kicks the mentioned user from the server", "usage": "{guildprefix}kick [user]\\n{guildprefix}kick [user] [reason]", "permission": "Kick Members", "module": "Moderation / Kick"}, {"name": "imute", "aliases": ["imgmute", "imagemute"], "description": "revoke someone", "usage": "{guildprefix}imute [user]\\n{guildprefix}imute [user] [reason]", "permission": "Moderate Members", "module": "Moderation / Mute"}, {"name": "iunmute", "aliases": ["imgunmute", "<PERSON>unmu<PERSON>"], "description": "restore someone", "usage": "{guildprefix}iunmute [user]\\n{guildprefix}iunmute [user] [reason]", "permission": "Moderate Members", "module": "Moderation / Mute"}, {"name": "mute", "aliases": ["m", "timeout"], "description": "timeout a user using Discord", "usage": "{guildprefix}mute [user] [duration] [reason]\\n{guildprefix}mute @user 10m spam\\n{guildprefix}mute @user 2d breaking rules\\n{guildprefix}mute @user 30s quick timeout", "permission": "Moderate Members", "module": "Moderation / Mute"}, {"name": "rmute", "aliases": ["reactmute", "reactionmute", "emojimute", "emotemute", "emute"], "description": "revoke someone", "usage": "{guildprefix}rmute [user]\\n{guildprefix}rmute [user] [reason]", "permission": "Moderate Members", "module": "Moderation / Mute"}, {"name": "runmute", "aliases": ["runreactionmute"], "description": "restore someone", "usage": "{guildprefix}runmute [user]\\n{guildprefix}runmute [user] [reason]", "permission": "Moderate Members", "module": "Moderation / Mute"}, {"name": "unmute", "aliases": ["untimeout"], "description": "remove timeout from a user using Discord", "usage": "{guildprefix}unmute [user] [reason]\\n{guildprefix}unmute @user appeal accepted", "permission": "Moderate Members", "module": "Moderation / Mute"}, {"name": "unwarn", "aliases": [], "description": "remove a warning from a user", "usage": "{guildprefix}unwarn [user] [case_id]\\n{guildprefix}unwarn [user] [case_id] [reason]", "permission": "Manage Messages", "module": "Moderation / Warn"}, {"name": "warn", "aliases": [], "description": "warn a user and add it to their moderation history", "usage": "{guildprefix}warn [user]\\n{guildprefix}warn [user] [reason]\\n{guildprefix}warn list [user]", "permission": "Manage Messages", "module": "Moderation / Warn"}, {"name": "ask", "aliases": [], "description": "Ask <PERSON> 3.5 Son<PERSON> a question", "usage": "{guildprefix}ask [question]\\n{guildprefix}ask (while replying to a message)", "permission": "Premium", "module": "Premium"}, {"name": "selfprefix", "aliases": ["sp"], "description": "Manage your personal prefix", "usage": "{guildprefix}selfprefix [set/remove] [prefix]", "permission": "Premium", "module": "Premium"}, {"name": "cleanup", "aliases": ["bc"], "description": "clean up bot messages and invoking commands in the channel", "usage": "{guildprefix}cleanup [number]", "permission": "Manage Messages", "module": "Purge"}, {"name": "purge", "aliases": ["clear", "c"], "description": "bulk delete messages from a channel", "usage": "{guildprefix}purge [number] (defaults to 50)\\n{guildprefix}purge bots [number]\\n{guildprefix}purge humans [number]\\n{guildprefix}purge @user [number]\\n{guildprefix}purge media [number]\\n{guildprefix}purge between [topMsgID] [lastMsgID]", "permission": "Manage Messages", "module": "Purge"}, {"name": "autoreact", "aliases": ["react"], "description": "automatically react to messages containing specific words", "usage": "{guildprefix}autoreact add [emoji] word\\n{guildprefix}autoreact remove [word]\\n{guildprefix}autoreact list\\n{guildprefix}autoreact reset", "permission": "Manage Channels", "module": "Server"}, {"name": "autoresponder", "aliases": ["autoresponse", "autorespond", "trigger"], "description": "automatically respond to specific triggers", "usage": "{guildprefix}autoresponder add [trigger], [response] [--options]\\n{guildprefix}autoresponder remove [trigger]\\n{guildprefix}autoresponder list\\n{guildprefix}autoresponder reset", "permission": "Manage Channels", "module": "Server"}, {"name": "autorole", "aliases": ["ar"], "description": "Set up automatic role assign on member join", "usage": "{guildprefix}autorole add [role]\\n{guildprefix}autorole remove [role] \\n{guildprefix}autorole list \\n{guildprefix}autorole reset", "permission": "Manage Guild & Manage Roles", "module": "Server"}, {"name": "boost", "aliases": ["boosts"], "description": "Set up a boost message and role for when members boost the server", "usage": "{guildprefix}boost add [channel] [message]\\n{guildprefix}boost remove [channel]\\n{guildprefix}boost view [channel]\\n{guildprefix}boost role [@role/none]\\n\\n{guildprefix}boost check\\n{guildprefix}boost variables", "permission": "Manage Guild", "module": "Server"}, {"name": "leave", "aliases": ["bye"], "description": "Set up a leave message for when members leave", "usage": "{guildprefix}leave add [channel] [message]\\n{guildprefix}leave remove [channel]\\n{guildprefix}leave view [channel]\\n\\n{guildprefix}leave check\\n{guildprefix}leave variables", "permission": "Manage Guild", "module": "Server"}, {"name": "vanity", "aliases": ["rep"], "description": "add vanity role to someone for reppin your server in their custom status", "usage": "{guildprefix}vanity set [vanity]\\n{guildprefix}vanity set none\\n\\n{guildprefix}vanity role [@role]\\n{guildprefix}vanity role none\\n\\n{guildprefix}vanity add [channel] [message]\\n{guildprefix}vanity remove [channel]\\n{guildprefix}vanity view [channel]\\n\\n{guildprefix}vanity check\\n{guildprefix}vanity variables", "permission": "Manage Guild", "module": "Server"}, {"name": "welcome", "aliases": ["welc"], "description": "Set up a welcome message for when new members join", "usage": "{guildprefix}welcome add [channel] [message]\\n{guildprefix}welcome remove [channel]\\n{guildprefix}welcome view [channel]\\n\\n{guildprefix}welcome check\\n{guildprefix}welcome variables", "permission": "Manage Guild", "module": "Server"}, {"name": "firstmessage", "aliases": ["first"], "description": "Get a link for the first message in a channel", "usage": "{guildprefix}firstmessage [channel]", "permission": null, "module": "Server / Channel"}, {"name": "hide", "aliases": ["hidechannel"], "description": "hides the channel for default/selected role", "usage": "{guildprefix}hide\\n{guildprefix}hide @role\\n{guildprefix}hide #channel @role", "permission": "Manage Channels", "module": "Server / Channel"}, {"name": "lock", "aliases": ["lockdown"], "description": "locks the channel for default/selected role", "usage": "{guildprefix}lock\\n{guildprefix}lock @role\\n{guildprefix}lock #channel @role", "permission": "Manage Channels", "module": "Server / Channel"}, {"name": "nuke", "aliases": [], "description": "deletes channel and remakes a new one (admin only)", "usage": "{guildprefix}nuke", "permission": "Manage Guild", "module": "Server / Channel"}, {"name": "pin", "aliases": [], "description": "pin a message in the current channel", "usage": "{guildprefix}pin - reply to a message to pin it", "permission": "Manage Messages", "module": "Server / Channel"}, {"name": "topic", "aliases": ["settopic", "channeltopic"], "description": "Set or clear the topic/description of a channel", "usage": "{guildprefix}topic [description]\\n{guildprefix}topic #channel [description]\\n{guildprefix}topic clear\\n{guildprefix}topic #channel clear", "permission": "Manage Channels", "module": "Server / Channel"}, {"name": "unhide", "aliases": ["unhidechannel"], "description": "Unhide the channel for default/selected role", "usage": "{guildprefix}unhide\\n{guildprefix}unhide @role\\n{guildprefix}unhide #channel @role", "permission": "Manage Channels", "module": "Server / Channel"}, {"name": "unlock", "aliases": ["unlockdown"], "description": "unlock the channel for default/selected role", "usage": "{guildprefix}unlock\\n{guildprefix}unlock @role\\n{guildprefix}unlock #channel @role", "permission": "Manage Channels", "module": "Server / Channel"}, {"name": "forcenick", "aliases": ["fn"], "description": "force a nickname on a user that will be automatically restored if they try to change it", "usage": "{guildprefix}forcenick [user] [nickname] - force a nickname\\n{guildprefix}forcenick [user] - remove forced nickname\\n{guildprefix}forcenick list - show all forced nicknames\\n{guildprefix}forcenick reset - remove all forced nicknames", "permission": "Manage Nicknames", "module": "Server / Names"}, {"name": "rename", "aliases": ["nick"], "description": "change a member", "usage": "{guildprefix}rename [user]\\n{guildprefix}rename [user] [nickname]", "permission": "Manage Nicknames", "module": "Server / Names"}, {"name": "avatar", "aliases": ["av", "pfp"], "description": "show a user", "usage": "{guildprefix}avatar [user]", "permission": null, "module": "Server / Profiles"}, {"name": "banner", "aliases": ["userbanner"], "description": "show a user", "usage": "{guildprefix}banner [user]", "permission": null, "module": "Server / Profiles"}, {"name": "guildbanner", "aliases": ["serverbanner2", "gbanner"], "description": "show a server", "usage": "{guildprefix}guildbanner [server id or name]", "permission": null, "module": "Server / Profiles"}, {"name": "guildicon", "aliases": ["servericon", "icon"], "description": "show a server", "usage": "{guildprefix}guildicon [server id or name]", "permission": null, "module": "Server / Profiles"}, {"name": "guildsplash", "aliases": ["serversplash", "splash"], "description": "show a server", "usage": "{guildprefix}guildsplash [server id or name]", "permission": null, "module": "Server / Profiles"}, {"name": "savatar", "aliases": ["serveravatar", "sav"], "description": "show a user", "usage": "{guildprefix}savatar [user]", "permission": null, "module": "Server / Profiles"}, {"name": "sbanner", "aliases": ["serverbanner"], "description": "show a user", "usage": "{guildprefix}sbanner [user]", "permission": null, "module": "Server / Profiles"}, {"name": "inrole", "aliases": ["members", "rolemembers"], "description": "get a list of users in a specific role", "usage": "{guildprefix}inrole [role]", "permission": null, "module": "Server / Roles"}, {"name": "pic", "aliases": [], "description": "toggle user pic permissions (ability to send images/embeds) in current channel", "usage": "{guildprefix}pic [user]", "permission": "Manage Channels", "module": "Server / Roles"}, {"name": "role", "aliases": ["r"], "description": "Comprehensive role management system", "usage": "{guildprefix}role [member] [role]\\n{guildprefix}role create [name]\\n{guildprefix}role delete [role]\\n{guildprefix}role rename [role] [new name]\\n{guildprefix}role color [role] [color]\\n{guildprefix}role icon [role] [emoji]\\n{guildprefix}role humans [role]\\n{guildprefix}role bots [role]\\n{guildprefix}role all [role]", "permission": "Manage Roles", "module": "Server / Roles"}, {"name": "steal", "aliases": ["stealemoji", "stealsticker"], "description": "Steal stickers and emotes from messages by replying to them", "usage": "{guildprefix}steal (reply to a message with stickers/emotes)", "permission": "Manage Expressions", "module": "Server / Steal"}, {"name": "setbanner", "aliases": ["serverbanner"], "description": "set the server banner (requires boost level 2+)", "usage": "{guildprefix}setbanner [attachment or URL]\\n{guildprefix}setbanner - remove server banner", "permission": "Manage Guild", "module": "Server / Theme"}, {"name": "seticon", "aliases": ["servericon"], "description": "set the server icon", "usage": "{guildprefix}seticon [attachment or URL]\\n{guildprefix}seticon - remove server icon", "permission": "Manage Guild", "module": "Server / Theme"}, {"name": "setsplash", "aliases": ["serversplash"], "description": "set the server invite splash screen (requires boost level 1+)", "usage": "{guildprefix}setsplash [attachment or URL]\\n{guildprefix}setsplash - remove server splash", "permission": "Manage Guild", "module": "Server / Theme"}, {"name": "botinfo", "aliases": ["bi", "info", "about"], "description": "Gets basic information about the bot", "usage": null, "permission": null, "module": "Service"}, {"name": "donate", "aliases": ["buy", "donator"], "description": "Feel free to donate if you like the bot!", "usage": null, "permission": null, "module": "Service"}, {"name": "feedback", "aliases": ["fb", "review", "suggest", "suggestion"], "description": "Send some a review/suggestion for the dev", "usage": "{guildprefix}feedback [comment]", "permission": null, "module": "Service"}, {"name": "invite", "aliases": ["inv"], "description": "Send an invite link of the bot", "usage": "{guildprefix}invite", "permission": null, "module": "Service"}, {"name": "report", "aliases": ["bug", "issue"], "description": "Report bugs/issues with the bot", "usage": "{guildprefix}report [comment]", "permission": null, "module": "Service"}, {"name": "support", "aliases": [], "description": "Get invite from official server", "usage": "{guildprefix}support", "permission": null, "module": "Service"}, {"name": "uptime", "aliases": [], "description": "check how long I has been running", "usage": "{guildprefix}uptime", "permission": null, "module": "Service"}, {"name": "clearsnipe", "aliases": ["cs", "clearsnipes"], "description": "clear snipe data for this channel", "usage": "{guildprefix}clearsnipe", "permission": null, "module": "Snipes"}, {"name": "editsnipe", "aliases": ["es"], "description": "snipes edited messages", "usage": "{guildprefix}editsnipe [number]", "permission": null, "module": "Snipes"}, {"name": "reactionsnipe", "aliases": ["rs", "reactsnipe"], "description": "snipes removed reactions", "usage": "{guildprefix}reactionsnipe [number]", "permission": null, "module": "Snipes"}, {"name": "snipe", "aliases": ["s"], "description": "snipes deleted messages", "usage": "{guildprefix}snipe [number]", "permission": null, "module": "Snipes"}, {"name": "tiktok", "aliases": ["tt"], "description": "Get information about a TikTok profile", "usage": "{guildprefix}tik<PERSON> [username]", "permission": null, "module": "Socials"}, {"name": "valorant", "aliases": ["valo", "val"], "description": "Get information about a Valorant player", "usage": "{guildprefix}valorant [username#tag]", "permission": null, "module": "Socials"}, {"name": "responder", "aliases": [], "description": "Download content when ", "usage": "adore [link]", "permission": null, "module": "Utility"}, {"name": "translate", "aliases": ["tr"], "description": "Translate text to different languages", "usage": "{guildprefix}tr - translate to English\\n{guildprefix}tr [language] - translate to specified language\\n{guildprefix}tr [text] - translate text to English\\n{guildprefix}tr [language] [text] - translate text to specified language\\nReply to a message to translate it", "permission": null, "module": "Utility"}]