/**
 * Main application script
 */
document.addEventListener('DOMContentLoaded', () => {
  // Add event listeners to all form inputs
  const formInputs = document.querySelectorAll('input, textarea');
  formInputs.forEach(input => {
    input.addEventListener('input', updatePreview);
  });
  
  // Initialize color picker
  const colorInput = document.getElementById('color');
  const colorTextInput = document.getElementById('colorInput');

  if (colorInput && colorTextInput) {
    // Start with no color selected
    colorInput.value = '#000000';
    colorTextInput.value = '';

    // Color picker change handler
    colorInput.addEventListener('input', (e) => {
      const color = e.target.value;
      colorTextInput.value = color;
      updatePreview();
    });

    // Text input change handler
    colorTextInput.addEventListener('input', (e) => {
      const color = e.target.value;
      if (color && /^#[0-9A-F]{6}$/i.test(color)) {
        colorInput.value = color;
      }
      updatePreview();
    });

    // Validate hex color format
    colorTextInput.addEventListener('blur', (e) => {
      const color = e.target.value;
      if (color && !/^#[0-9A-F]{6}$/i.test(color)) {
        // Try to fix common issues
        let fixedColor = color.replace(/[^0-9A-Fa-f#]/g, '');
        if (!fixedColor.startsWith('#')) {
          fixedColor = '#' + fixedColor;
        }
        if (fixedColor.length === 4) {
          // Convert #RGB to #RRGGBB
          fixedColor = '#' + fixedColor[1] + fixedColor[1] + fixedColor[2] + fixedColor[2] + fixedColor[3] + fixedColor[3];
        }
        if (/^#[0-9A-F]{6}$/i.test(fixedColor)) {
          e.target.value = fixedColor;
          colorInput.value = fixedColor;
          updatePreview();
        } else if (color !== '') {
          // Invalid color, clear it
          e.target.value = '';
          updatePreview();
        }
      }
    });
  }
  
  // Initialize timestamp checkbox
  const timestampEnabled = document.getElementById('timestampEnabled');

  timestampEnabled.addEventListener('change', () => {
    updatePreview();
  });

  // Initialize text size controls
  const decreaseBtn = document.getElementById('decreaseSize');
  const increaseBtn = document.getElementById('increaseSize');
  const description = document.getElementById('description');

  let currentFontSize = 14; // Default font size in pixels

  if (decreaseBtn && increaseBtn && description) {
    decreaseBtn.addEventListener('click', (e) => {
      e.preventDefault();
      if (currentFontSize > 10) {
        currentFontSize -= 1;
        description.style.fontSize = currentFontSize + 'px';
        // Add smooth transition
        description.style.transition = 'font-size 0.2s ease';
        setTimeout(() => {
          if (typeof updatePreview === 'function') {
            updatePreview();
          }
        }, 50);
      }
    });

    increaseBtn.addEventListener('click', (e) => {
      e.preventDefault();
      if (currentFontSize < 24) {
        currentFontSize += 1;
        description.style.fontSize = currentFontSize + 'px';
        // Add smooth transition
        description.style.transition = 'font-size 0.2s ease';
        setTimeout(() => {
          if (typeof updatePreview === 'function') {
            updatePreview();
          }
        }, 50);
      }
    });

    // Reset font size when form is reset
    window.resetDescriptionFontSize = () => {
      currentFontSize = 14;
      description.style.fontSize = '';
      description.style.transition = '';
    };
  }
  
  // Initialize everything
  updatePreview();
  
  // Create CSS for notifications
  const style = document.createElement('style');
  style.textContent = `
    .notification {
      position: fixed;
      bottom: 20px;
      right: 20px;
      padding: 12px 20px;
      background-color: var(--accent-color);
      color: white;
      border-radius: var(--radius-md);
      box-shadow: var(--shadow-md);
      opacity: 0;
      transform: translateY(20px);
      transition: opacity 0.3s, transform 0.3s;
      z-index: 1000;
    }
    
    .notification.error {
      background-color: var(--red);
    }
    
    .notification.show {
      opacity: 1;
      transform: translateY(0);
    }
  `;
  document.head.appendChild(style);
  
  // Add animation to embed preview on hover
  const embedPreview = document.querySelector('.embed-preview-container');
  embedPreview.addEventListener('mouseenter', () => {
    const embed = embedPreview.querySelector('.embed');
    embed.style.transform = 'scale(1.02)';
    embed.style.transition = 'transform 0.3s ease';
  });
  
  embedPreview.addEventListener('mouseleave', () => {
    const embed = embedPreview.querySelector('.embed');
    embed.style.transform = 'scale(1)';
  });
});
