<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Adore - Spotify Integration</title>
    <link rel="stylesheet" href="css/main.css">
    <style>
        .spotify-container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            text-align: center;
        }
        
        .spotify-header {
            background: linear-gradient(135deg, #1db954, #1ed760);
            color: white;
            padding: 30px;
            border-radius: 15px;
            margin-bottom: 30px;
        }
        
        .spotify-header h1 {
            margin: 0;
            font-size: 2.5em;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 15px;
        }
        
        .token-display {
            background: #f8f9fa;
            border: 2px solid #1db954;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            word-break: break-all;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            line-height: 1.5;
        }
        
        .copy-button {
            background: #1db954;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 25px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 10px;
        }
        
        .copy-button:hover {
            background: #1ed760;
            transform: translateY(-2px);
        }
        
        .copy-button:active {
            transform: translateY(0);
        }
        
        .copy-button.copied {
            background: #28a745;
        }
        
        .instructions {
            background: white;
            border-radius: 10px;
            padding: 25px;
            margin: 20px 0;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            text-align: left;
        }
        
        .step {
            display: flex;
            align-items: flex-start;
            margin: 15px 0;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 8px;
            border-left: 4px solid #1db954;
        }
        
        .step-number {
            background: #1db954;
            color: white;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-right: 15px;
            flex-shrink: 0;
        }
        
        .error-message {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
            border-radius: 8px;
            padding: 15px;
            margin: 20px 0;
        }
        
        .success-message {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
            border-radius: 8px;
            padding: 15px;
            margin: 20px 0;
        }
        
        .loading {
            display: none;
            color: #1db954;
            font-size: 18px;
            margin: 20px 0;
        }
        
        .discord-link {
            background: #5865f2;
            color: white;
            text-decoration: none;
            padding: 12px 24px;
            border-radius: 25px;
            font-weight: bold;
            display: inline-block;
            margin: 10px;
            transition: all 0.3s ease;
        }
        
        .discord-link:hover {
            background: #4752c4;
            transform: translateY(-2px);
        }
    </style>
</head>
<body>
    <div class="spotify-container">
        <div class="spotify-header">
            <h1>
                🎵 Spotify Integration
            </h1>
            <p>Connect your Spotify account to Adore Discord Bot</p>
        </div>

        <div id="loading" class="loading">
            🔄 Processing your Spotify authorization...
        </div>

        <div id="error" class="error-message" style="display: none;">
            <h3>❌ Authorization Failed</h3>
            <p id="error-text"></p>
        </div>

        <div id="success" class="success-message" style="display: none;">
            <h3>✅ Token Retrieved Successfully!</h3>
            <p>Your Spotify access token has been extracted from the URL. Copy it below and paste it into Discord.</p>
        </div>

        <div id="token-section" style="display: none;">
            <h3>🔑 Your Spotify Access Token</h3>
            <div class="token-display" id="token-display">
                <!-- Token will be displayed here -->
            </div>
            <button class="copy-button" id="copy-button" onclick="copyToken()">
                📋 Copy Token
            </button>
        </div>

        <div class="instructions">
            <h3>📱 How to use this token:</h3>
            
            <div class="step">
                <div class="step-number">1</div>
                <div>
                    <strong>Copy the token above</strong><br>
                    Click the "Copy Token" button to copy your access token to clipboard
                </div>
            </div>
            
            <div class="step">
                <div class="step-number">2</div>
                <div>
                    <strong>Go back to Discord</strong><br>
                    Return to the Discord server where you ran the <code>,spotify login</code> command
                </div>
            </div>
            
            <div class="step">
                <div class="step-number">3</div>
                <div>
                    <strong>Click "Add Spotify Token"</strong><br>
                    Click the button in the bot's message to open the token input modal
                </div>
            </div>
            
            <div class="step">
                <div class="step-number">4</div>
                <div>
                    <strong>Paste and submit</strong><br>
                    Paste your token in the Discord modal and click submit
                </div>
            </div>
            
            <div class="step">
                <div class="step-number">5</div>
                <div>
                    <strong>Start using Spotify commands!</strong><br>
                    Try <code>,spotify</code> to see your current song or <code>,spotify play [song]</code> to play music
                </div>
            </div>
        </div>

        <div style="margin-top: 30px;">
            <a href="#" class="discord-link" onclick="window.close()">
                🔙 Back to Discord
            </a>
        </div>

        <div style="margin-top: 20px; color: #666; font-size: 14px;">
            <p>⚠️ <strong>Security Note:</strong> This token expires after 1 hour. You can regenerate it anytime by running <code>,spotify login</code> again.</p>
            <p>🔒 Your token is only used for Spotify API requests and is stored securely by Adore.</p>
        </div>
    </div>

    <script>
        // Extract token from URL parameters
        function extractTokenFromURL() {
            const urlParams = new URLSearchParams(window.location.search);
            const fragment = new URLSearchParams(window.location.hash.substring(1));
            
            // Try to get token from URL parameters or fragment
            let token = urlParams.get('access_token') || fragment.get('access_token');
            
            if (!token) {
                // Check for error
                const error = urlParams.get('error') || fragment.get('error');
                if (error) {
                    showError(`Authorization failed: ${error}`);
                    return;
                }
                
                showError('No access token found in URL. Please make sure you completed the Spotify authorization process.');
                return;
            }
            
            showSuccess(token);
        }
        
        function showError(message) {
            document.getElementById('loading').style.display = 'none';
            document.getElementById('error').style.display = 'block';
            document.getElementById('error-text').textContent = message;
        }
        
        function showSuccess(token) {
            document.getElementById('loading').style.display = 'none';
            document.getElementById('success').style.display = 'block';
            document.getElementById('token-section').style.display = 'block';
            document.getElementById('token-display').textContent = token;
            
            // Store token for copying
            window.spotifyToken = token;
        }
        
        function copyToken() {
            const token = window.spotifyToken;
            if (!token) return;
            
            navigator.clipboard.writeText(token).then(() => {
                const button = document.getElementById('copy-button');
                const originalText = button.textContent;
                button.textContent = '✅ Copied!';
                button.classList.add('copied');
                
                setTimeout(() => {
                    button.textContent = originalText;
                    button.classList.remove('copied');
                }, 2000);
            }).catch(err => {
                // Fallback for older browsers
                const textArea = document.createElement('textarea');
                textArea.value = token;
                document.body.appendChild(textArea);
                textArea.select();
                document.execCommand('copy');
                document.body.removeChild(textArea);
                
                const button = document.getElementById('copy-button');
                const originalText = button.textContent;
                button.textContent = '✅ Copied!';
                button.classList.add('copied');
                
                setTimeout(() => {
                    button.textContent = originalText;
                    button.classList.remove('copied');
                }, 2000);
            });
        }
        
        // Initialize when page loads
        document.addEventListener('DOMContentLoaded', function() {
            document.getElementById('loading').style.display = 'block';
            setTimeout(extractTokenFromURL, 500); // Small delay to ensure URL is fully loaded
        });
    </script>
</body>
</html>
