<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no, viewport-fit=cover">
  <title>Adore - Invite now</title>
  <meta name="description" content="Adore is <PERSON>rd's ultimate all-in-one app">
  <link rel="stylesheet" href="css/main.css">
  <link rel="stylesheet" href="css/background.css">
  <link rel="stylesheet" href="css/form.css">
  <link rel="stylesheet" href="css/preview.css">
  <link rel="stylesheet" href="css/homepage.css">
  <link rel="stylesheet" href="css/performance.css">
  <link rel="stylesheet" href="css/no-bounce.css">
  <link rel="stylesheet" href="css/ios-fixes.css">
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
  <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">

</head>
<body>
  <!-- Background -->
  <div class="page-background">
    <div class="stars"></div>
    <div class="twinkling"></div>
    <div class="milky-way"></div>
  </div>

  <!-- Navigation -->
  <nav class="navbar animate-on-load">
    <div class="nav-links">
      <a href="#home" class="nav-link active">Home</a>
      <a href="#features" class="nav-link">Features</a>
      <a href="builder.html" class="nav-link">Builder</a>
      <a href="commands.html" class="nav-link">Commands</a>
      <a href="https://adore.gitbook.io/adore-docs" class="nav-link" target="_blank">Docs</a>
    </div>
    <div class="mobile-menu-toggle">
      <i class="fas fa-bars"></i>
    </div>
  </nav>

  <!-- Hero Section -->
  <section id="home" class="hero-section">
    <div class="hero-background">
    </div>
    <div class="hero-container">
      <div class="hero-content">

        <h1 class="hero-title animate-on-load delay-200">
          <span class="gradient-text">Adore</span> is Discord's<br>
          ultimate all-in-one app
        </h1>
        <p class="hero-description animate-on-load delay-300">
          Simple, yet advanced, feature-rich and completely free Discord Application.
        </p>
        <div class="hero-actions animate-on-load delay-400">
          <button class="btn-primary hero-cta" onclick="window.open('https://discord.com/oauth2/authorize?client_id=1378983071650680872', '_blank')"
            <i class="fas fa-plus"></i>
            Invite Adore
          </button>
          <button class="btn-secondary" onclick="window.location.href='builder.html'">
            <i class="fas fa-tools"></i>
            Embed Builder
          </button>
        </div>

      </div>



    </div>
  </section>



  <!-- Top Servers Section -->
  <section class="top-servers-section">
    <div class="servers-container">
      <div class="section-header animate-on-scroll">
        <h2 class="section-title">Trusted by Top Discord Servers</h2>
        <p class="section-description">Join thousands of servers already using Adore</p>
      </div>
      <div class="servers-scroll-container">
        <div class="servers-track" id="servers-track">
          <!-- Server cards will be dynamically loaded from servers.json -->
        </div>
      </div>
    </div>
  </section>

  <!-- Platform Integrations Section -->
  <section class="integrations-section">
    <div class="integrations-container">
      <div class="section-header animate-on-scroll">
        <h2 class="section-title">Integrate with Your Favorite Platforms</h2>
        <p class="section-description">Seamlessly connect with the platforms you love</p>
      </div>
      <div class="integrations-scroll-container">
        <div class="integrations-track">
          <div class="integration-card">
            <div class="integration-header">
              <div class="integration-icon spotify">
                <i class="fab fa-spotify"></i>
              </div>
              <div class="integration-name">Spotify</div>
            </div>
            <div class="integration-description">Track info, playlists, artist data, album covers, listening activity</div>
          </div>
          <div class="integration-card">
            <div class="integration-header">
              <div class="integration-icon twitter">
                <i class="fab fa-twitter"></i>
              </div>
              <div class="integration-name">X (Twitter)</div>
            </div>
            <div class="integration-description">Tweets, user profiles, follower counts, trending topics, media</div>
          </div>
          <div class="integration-card">
            <div class="integration-header">
              <div class="integration-icon soundcloud">
                <i class="fab fa-soundcloud"></i>
              </div>
              <div class="integration-name">SoundCloud</div>
            </div>
            <div class="integration-description">Track details, artist info, play counts, waveforms, comments</div>
          </div>
          <div class="integration-card">
            <div class="integration-header">
              <div class="integration-icon instagram">
                <i class="fab fa-instagram"></i>
              </div>
              <div class="integration-name">Instagram</div>
            </div>
            <div class="integration-description">Posts, stories, user profiles, follower stats, media content</div>
          </div>
          <div class="integration-card">
            <div class="integration-header">
              <div class="integration-icon pinterest">
                <i class="fab fa-pinterest"></i>
              </div>
              <div class="integration-name">Pinterest</div>
            </div>
            <div class="integration-description">Pins, boards, user profiles, trending content, image data</div>
          </div>
          <div class="integration-card">
            <div class="integration-header">
              <div class="integration-icon tiktok">
                <i class="fab fa-tiktok"></i>
              </div>
              <div class="integration-name">TikTok</div>
            </div>
            <div class="integration-description">Video info, user profiles, view counts, trending sounds, hashtags</div>
          </div>
          <div class="integration-card">
            <div class="integration-header">
              <div class="integration-icon lastfm">
                <i class="fab fa-lastfm"></i>
              </div>
              <div class="integration-name">Last.fm</div>
            </div>
            <div class="integration-description">Scrobbles, top tracks, artist stats, music history, recommendations</div>
          </div>
          <!-- Duplicate for seamless loop -->
          <div class="integration-card">
            <div class="integration-header">
              <div class="integration-icon spotify">
                <i class="fab fa-spotify"></i>
              </div>
              <div class="integration-name">Spotify</div>
            </div>
            <div class="integration-description">Track info, playlists, artist data, album covers, listening activity</div>
          </div>
          <div class="integration-card">
            <div class="integration-header">
              <div class="integration-icon twitter">
                <i class="fab fa-twitter"></i>
              </div>
              <div class="integration-name">X (Twitter)</div>
            </div>
            <div class="integration-description">Tweets, user profiles, follower counts, trending topics, media</div>
          </div>
          <div class="integration-card">
            <div class="integration-header">
              <div class="integration-icon soundcloud">
                <i class="fab fa-soundcloud"></i>
              </div>
              <div class="integration-name">SoundCloud</div>
            </div>
            <div class="integration-description">Track details, artist info, play counts, waveforms, comments</div>
          </div>
          <div class="integration-card">
            <div class="integration-header">
              <div class="integration-icon instagram">
                <i class="fab fa-instagram"></i>
              </div>
              <div class="integration-name">Instagram</div>
            </div>
            <div class="integration-description">Posts, stories, user profiles, follower stats, media content</div>
          </div>
        </div>
      </div>
    </div>
  </section>



  <!-- Music Features Section -->
  <section class="music-section">
    <div class="music-container">
      <div class="section-header animate-on-scroll">
        <h2 class="section-title">Music Features Built For Everyone</h2>
        <p class="section-description">Premium audio quality and commands for a superior music experience</p>
      </div>

      <div class="music-grid">
        <div class="music-card animate-on-scroll">
          <div class="music-icon">
            <i class="fas fa-sliders-h"></i>
          </div>
          <div class="music-content">
            <h3 class="music-title">Preset Filters</h3>
            <p class="music-description">
              On-the-fly generation to transform any music into an immersive listening experience.
            </p>
          </div>
          <div class="music-visual">
            <div class="filters-demo">
              <div class="filter-item active">
                <div class="filter-icon">
                  <i class="fas fa-music"></i>
                </div>
                <span>Bass Boost</span>
                <div class="filter-indicator"></div>
              </div>
              <div class="filter-item">
                <div class="filter-icon">
                  <i class="fas fa-headphones"></i>
                </div>
                <span>8D Audio</span>
                <div class="filter-indicator"></div>
              </div>
              <div class="filter-item">
                <div class="filter-icon">
                  <i class="fas fa-volume-up"></i>
                </div>
                <span>Nightcore</span>
                <div class="filter-indicator"></div>
              </div>
              <div class="filter-item">
                <div class="filter-icon">
                  <i class="fas fa-wave-square"></i>
                </div>
                <span>Vaporwave</span>
                <div class="filter-indicator"></div>
              </div>
            </div>
          </div>
        </div>

        <div class="music-card animate-on-scroll delay-100">
          <div class="music-icon">
            <i class="fas fa-list"></i>
          </div>
          <div class="music-content">
            <h3 class="music-title">Queue</h3>
            <p class="music-description">
              Listen to music after music, uninterrupted, all day.
            </p>
          </div>
          <div class="music-visual">
            <div class="queue-demo">
              <div class="queue-header">
                <i class="fas fa-play"></i>
                <span>Now Playing</span>
              </div>
              <div class="current-track">
                <div class="track-artwork">
                  <i class="fas fa-music"></i>
                </div>
                <div class="track-info">
                  <div class="track-name">Blinding Lights</div>
                  <div class="track-artist">The Weeknd</div>
                </div>
                <div class="track-duration">3:20</div>
              </div>
              <div class="queue-list">
                <div class="queue-item">
                  <span class="queue-number">1</span>
                  <span class="queue-track">Levitating - Dua Lipa</span>
                  <span class="queue-time">3:23</span>
                </div>
                <div class="queue-item">
                  <span class="queue-number">2</span>
                  <span class="queue-track">Good 4 U - Olivia Rodrigo</span>
                  <span class="queue-time">2:58</span>
                </div>
                <div class="queue-item">
                  <span class="queue-number">3</span>
                  <span class="queue-track">Stay - The Kid LAROI</span>
                  <span class="queue-time">2:21</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="music-card animate-on-scroll delay-200">
          <div class="music-icon spotify-icon">
            <i class="fab fa-spotify"></i>
          </div>
          <div class="music-content">
            <h3 class="music-title">Spotify</h3>
            <p class="music-description">
              Fully integrated and capable, with Spotify.
            </p>
          </div>
          <div class="music-visual">
            <div class="spotify-demo">
              <div class="spotify-header">
                <div class="spotify-logo">
                  <i class="fab fa-spotify"></i>
                </div>
                <span>Spotify Integration</span>
              </div>
              <div class="spotify-player">
                <div class="spotify-track">
                  <div class="spotify-artwork">
                    <i class="fas fa-music"></i>
                  </div>
                  <div class="spotify-info">
                    <div class="spotify-name">As It Was</div>
                    <div class="spotify-artist">Harry Styles</div>
                  </div>
                </div>
                <div class="spotify-controls">
                  <div class="spotify-btn">
                    <i class="fas fa-step-backward"></i>
                  </div>
                  <div class="spotify-btn play">
                    <i class="fas fa-pause"></i>
                  </div>
                  <div class="spotify-btn">
                    <i class="fas fa-step-forward"></i>
                  </div>
                </div>
                <div class="spotify-progress">
                  <div class="progress-bar">
                    <div class="progress-fill"></div>
                  </div>
                  <div class="progress-time">
                    <span>1:23</span>
                    <span>2:58</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>



  <!-- Powerful Features Section -->
  <section id="features" class="powerful-features-section">
    <div class="powerful-features-container">
      <div class="section-header animate-on-scroll">
        <h2 class="section-title">Powerful Features</h2>
        <p class="section-description">Everything you need to create professional Discord embeds and enhance your server</p>
      </div>

      <div class="powerful-features-grid">
        <div class="powerful-feature-card large-card animate-on-scroll">
          <div class="powerful-feature-icon">
            <i class="fas fa-eye"></i>
          </div>
          <h3 class="powerful-feature-title">Real-time Preview</h3>
          <p class="powerful-feature-description">
            See your embed exactly as it will appear in Discord with live updates as you type. No guesswork, just perfect embeds.
          </p>
        </div>

        <div class="powerful-feature-card animate-on-scroll delay-100">
          <div class="powerful-feature-icon">
            <i class="fas fa-palette"></i>
          </div>
          <h3 class="powerful-feature-title">Custom Styling</h3>
          <p class="powerful-feature-description">
            Full color customization, custom fields, buttons, and advanced formatting options.
          </p>
        </div>



        <div class="powerful-feature-card animate-on-scroll delay-300">
          <div class="powerful-feature-icon">
            <i class="fas fa-shield-alt"></i>
          </div>
          <h3 class="powerful-feature-title">Advanced Moderation</h3>
          <p class="powerful-feature-description">
            Comprehensive moderation tools with auto-mod, custom filters, and detailed logging systems.
          </p>
        </div>



        <div class="powerful-feature-card animate-on-scroll delay-400">
          <div class="powerful-feature-icon">
            <i class="fas fa-mobile-alt"></i>
          </div>
          <h3 class="powerful-feature-title">Mobile Friendly</h3>
          <p class="powerful-feature-description">
            Fully responsive design that works perfectly on desktop, tablet, and mobile devices.
          </p>
        </div>

        <div class="powerful-feature-card animate-on-scroll delay-500">
          <div class="powerful-feature-icon">
            <i class="fas fa-gamepad"></i>
          </div>
          <h3 class="powerful-feature-title">Fun & Games</h3>
          <p class="powerful-feature-description">
            Interactive games, trivia, polls, and entertainment features to engage your community.
          </p>
        </div>

        <div class="powerful-feature-card animate-on-scroll delay-600">
          <div class="powerful-feature-icon">
            <i class="fas fa-bolt"></i>
          </div>
          <h3 class="powerful-feature-title">Lightning Fast</h3>
          <p class="powerful-feature-description">
            Optimized performance with instant updates and smooth animations for the best user experience.
          </p>
        </div>
      </div>
    </div>
  </section>





  <script src="js/ios-detection.js"></script>
  <script src="js/navigation.js"></script>
  <script src="js/servers.js"></script>
  <script src="js/animations.js"></script>
</body>
</html>
