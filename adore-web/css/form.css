.form-section {
  position: relative;
}

.form-section .glass-card {
  height: auto;
}

.form-group {
  margin-bottom: 16px;
}

.form-row {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 12px;
  margin-bottom: 16px;
}

label {
  display: block;
  margin-bottom: 6px;
  color: #b5bac1;
  font-size: 14px;
  font-weight: 500;
  letter-spacing: 0;
}

input[type="text"],
input[type="url"],
input[type="email"],
input[type="datetime-local"],
textarea {
  width: 100%;
  padding: 12px;
  background: #1a1a1a;
  color: #ffffff;
  border: 1px solid #404040;
  border-radius: 6px;
  font-family: inherit;
  font-size: 14px;
  position: relative;
  transition: border-color 0.15s ease;
  box-sizing: border-box;
}



input[type="text"]:focus,
input[type="url"]:focus,
input[type="email"]:focus,
input[type="datetime-local"]:focus,
textarea:focus {
  outline: none;
  border-color: #729bb0;
}

textarea {
  min-height: 80px;
  resize: vertical;
  font-family: inherit;
}

.form-section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-sm);
  padding-top: var(--spacing-md);
  border-top: 1px solid rgba(79, 84, 92, 0.1);
}

.add-button {
  background-color: rgba(25, 27, 30, 0.9);
  border: none;
  color: var(--text-primary);
  padding: var(--spacing-md) var(--spacing-lg);
  border-radius: var(--radius-md);
  font-size: 0.9rem;
  font-weight: 500;
  transition: var(--transition);
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
}

.add-button:hover {
  background-color: rgba(30, 32, 35, 0.95);
}

.add-button:before {
  content: '+';
  font-size: 1.1rem;
  font-weight: bold;
}

/* Color picker styles */
.color-picker-container {
  display: grid;
  grid-template-columns: auto 1fr;
  gap: var(--spacing-md);
  align-items: center;
}

input[type="color"] {
  -webkit-appearance: none;
  width: 50px;
  height: 50px;
  border: none;
  border-radius: var(--radius-md);
  cursor: pointer;
  padding: 0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
  transition: var(--transition);
}

input[type="color"]:hover {
  transform: scale(1.05);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.4);
}

input[type="color"]::-webkit-color-swatch-wrapper {
  padding: 0;
}

input[type="color"]::-webkit-color-swatch {
  border: none;
  border-radius: var(--radius-md);
}

input[type="color"]::-moz-color-swatch {
  border: none;
  border-radius: var(--radius-md);
}

input[type="text"].color-input {
  width: 100px;
  margin-left: var(--spacing-sm);
}

/* Timestamp styles */
.timestamp-group {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.timestamp-group input[type="datetime-local"] {
  display: none;
}

input[type="checkbox"] {
  appearance: none;
  -webkit-appearance: none;
  width: 20px;
  height: 20px;
  border: none;
  border-radius: var(--radius-sm);
  background-color: rgba(25, 27, 30, 0.9);
  cursor: pointer;
  position: relative;
  transition: var(--transition);
  box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.3);
}

input[type="checkbox"]:checked {
  background-color: var(--accent-color);
  box-shadow: 0 2px 8px rgba(88, 101, 242, 0.3);
}

input[type="checkbox"]:checked::after {
  content: '✓';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: white;
  font-size: 14px;
  font-weight: bold;
}

input[type="checkbox"]:hover:not(:checked) {
  background-color: rgba(30, 32, 35, 0.95);
  transform: scale(1.05);
}

input[type="checkbox"]:checked:hover {
  background-color: var(--accent-hover);
  transform: scale(1.05);
}

.checkbox-label {
  display: inline;
  margin-left: var(--spacing-xs);
  cursor: pointer;
}

/* Hide datetime input */
input[type="datetime-local"] {
  display: none;
}

/* Field and button item styles */
.field-item, .button-item {
  background: rgba(63, 114, 129, 0.05);
  border: 1px solid rgba(63, 114, 129, 0.2);
  padding: var(--spacing-lg);
  border-radius: var(--radius-md);
  margin-bottom: var(--spacing-lg);
  position: relative;
  transition: var(--transition);
  backdrop-filter: blur(10px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.field-item::before, .button-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg, transparent, var(--accent-color), transparent);
  opacity: 0;
  transition: var(--transition);
  border-radius: var(--radius-md) var(--radius-md) 0 0;
}

.field-item:hover, .button-item:hover {
  background: rgba(63, 114, 129, 0.08);
  border-color: rgba(63, 114, 129, 0.4);
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.3);
}

.field-item:hover::before, .button-item:hover::before {
  opacity: 1;
}

.field-header, .button-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-md);
  padding-bottom: var(--spacing-sm);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.field-title, .button-title {
  font-weight: 600;
  color: var(--text-primary);
  font-size: 1rem;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.remove-button {
  color: #ff6b6b;
  background: rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(255, 107, 107, 0.3);
  cursor: pointer;
  font-size: 1.1rem;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: var(--transition);
  backdrop-filter: blur(10px);
  font-weight: bold;
}

.remove-button:hover {
  background: linear-gradient(135deg, #ff6b6b, #ff5252);
  color: #ffffff;
  border-color: #ff6b6b;
  transform: scale(1.1);
  box-shadow: 0 4px 12px rgba(255, 107, 107, 0.4);
}

.inline-checkbox {
  display: flex;
  align-items: center;
  margin-top: var(--spacing-md);
  gap: var(--spacing-sm);
  padding: var(--spacing-sm);
  background: rgba(63, 114, 129, 0.05);
  border-radius: var(--radius-sm);
  transition: var(--transition);
  cursor: pointer;
}

.inline-checkbox:hover {
  background: rgba(63, 114, 129, 0.08);
}

.inline-checkbox input[type="checkbox"] {
  width: 20px;
  height: 20px;
  background: rgba(0, 0, 0, 0.3);
  border: 2px solid rgba(255, 255, 255, 0.2);
  border-radius: var(--radius-sm);
  cursor: pointer;
  position: relative;
  appearance: none;
  -webkit-appearance: none;
  -moz-appearance: none;
  transition: var(--transition);
  flex-shrink: 0;
}

.inline-checkbox input[type="checkbox"]:hover {
  border-color: rgba(63, 114, 129, 0.5);
  background: rgba(63, 114, 129, 0.1);
}

.inline-checkbox input[type="checkbox"]:checked {
  background: linear-gradient(135deg, var(--accent-color), #7289DA);
  border-color: var(--accent-color);
  box-shadow: 0 2px 8px rgba(63, 114, 129, 0.3);
}

.inline-checkbox input[type="checkbox"]:checked::after {
  content: '✓';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: #ffffff;
  font-size: 12px;
  font-weight: bold;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.inline-checkbox label {
  margin: 0;
  color: var(--text-primary);
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  user-select: none;
  transition: var(--transition);
}

.inline-checkbox:hover label {
  color: var(--accent-color);
}

/* Button type selection */
.button-type-selector {
  display: flex;
  gap: var(--spacing-sm);
  margin-bottom: var(--spacing-md);
  flex-wrap: wrap;
  padding: var(--spacing-sm);
  background: rgba(63, 114, 129, 0.05);
  border-radius: var(--radius-md);
  border: 1px solid rgba(63, 114, 129, 0.2);
}

.button-type {
  padding: var(--spacing-sm) var(--spacing-md);
  border-radius: var(--radius-sm);
  background: rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(255, 255, 255, 0.1);
  color: var(--text-secondary);
  cursor: pointer;
  font-size: 0.85rem;
  font-weight: 500;
  transition: var(--transition);
  min-width: 60px;
  text-align: center;
  backdrop-filter: blur(10px);
}

.button-type:hover {
  background: rgba(63, 114, 129, 0.2);
  border-color: rgba(63, 114, 129, 0.4);
  color: var(--text-primary);
  transform: translateY(-1px);
}

.button-type.active {
  background: linear-gradient(135deg, var(--accent-color), #7289DA);
  border-color: var(--accent-color);
  color: #ffffff;
  box-shadow: 0 2px 8px rgba(63, 114, 129, 0.3);
  transform: translateY(-1px);
}