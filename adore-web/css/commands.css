/* Commands Page Styles */

.commands-page {
  min-height: 100vh;
  position: relative;
  overflow-x: hidden;
}











/* Commands Hero Section */
.commands-hero {
  min-height: 80vh;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  padding: 120px 0 60px;
}

.commands-hero-content {
  max-width: 800px;
  margin: 0 auto;
  padding: 0 var(--spacing-lg);
  text-align: center;
  position: relative;
}

.commands-badge {
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-xs);
  background: rgba(88, 101, 242, 0.1);
  border: 1px solid rgba(88, 101, 242, 0.3);
  padding: var(--spacing-xs) var(--spacing-md);
  border-radius: 50px;
  color: var(--accent-color);
  font-size: 0.9rem;
  font-weight: 600;
  margin-bottom: var(--spacing-lg);
  animation: pulse 2s ease-in-out infinite;
}

.commands-title {
  font-size: 3.5rem;
  font-weight: 800;
  margin-bottom: var(--spacing-lg);
  line-height: 1.1;
  color: var(--text-primary);
}

.commands-description {
  font-size: 1.2rem;
  color: var(--text-secondary);
  max-width: 600px;
  margin: 0 auto var(--spacing-xl);
  line-height: 1.6;
}

.commands-actions {
  display: flex;
  gap: var(--spacing-md);
  justify-content: center;
  align-items: center;
  flex-wrap: wrap;
  margin-bottom: var(--spacing-2xl);
}

.curvy-btn {
  padding: var(--spacing-md) var(--spacing-2xl);
  border-radius: 50px;
  font-weight: 700;
  font-size: 1.1rem;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-sm);
  transition: var(--transition);
  border: none;
  cursor: pointer;

}

.btn-primary.curvy-btn {
  background: linear-gradient(135deg, var(--accent-hover), var(--accent-color));
  color: white;
  padding: var(--spacing-md) var(--spacing-2xl);
  border-radius: 50px;
  font-weight: 700;
  font-size: 1.1rem;
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  transition: var(--transition);
  border: 1px solid rgba(255, 255, 255, 0.2);
  cursor: pointer;
  box-shadow:
    0 8px 25px rgba(63, 114, 129, 0.4),
    inset 0 1px 0 rgba(255, 255, 255, 0.3),
    inset 0 -1px 0 rgba(0, 0, 0, 0.1);
}

.btn-primary.curvy-btn:hover {
  transform: translateY(-3px) scale(1.05);
  box-shadow:
    0 12px 35px rgba(63, 114, 129, 0.6),
    inset 0 1px 0 rgba(255, 255, 255, 0.4),
    inset 0 -1px 0 rgba(0, 0, 0, 0.2);
  border-color: rgba(255, 255, 255, 0.3);
}

.btn-secondary.curvy-btn {
  background: var(--card-bg);
  color: var(--text-primary);
  padding: var(--spacing-md) var(--spacing-2xl);
  border-radius: 50px;
  font-weight: 700;
  font-size: 1.1rem;
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  transition: var(--transition);
  border: 1px solid var(--border-color);
  cursor: pointer;
  backdrop-filter: blur(10px);
  box-shadow:
    0 8px 25px rgba(0, 0, 0, 0.4),
    inset 0 1px 0 rgba(63, 114, 129, 0.3),
    inset 0 -1px 0 rgba(0, 0, 0, 0.3);
}

.btn-secondary.curvy-btn:hover {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.25), rgba(255, 255, 255, 0.1));
  border-color: rgba(255, 255, 255, 0.5);
  transform: translateY(-3px) scale(1.05);
  box-shadow:
    0 12px 35px rgba(0, 0, 0, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.5),
    inset 0 -1px 0 rgba(0, 0, 0, 0.2);
}

.commands-stats {
  display: flex;
  gap: var(--spacing-2xl);
  justify-content: center;
  align-items: center;
  flex-wrap: wrap;
  margin-bottom: var(--spacing-2xl);
}

.stat-item {
  text-align: center;
  background: var(--card-bg);
  border: 1px solid var(--border-color);
  border-radius: 50px;
  padding: var(--spacing-xl) var(--spacing-2xl);
  backdrop-filter: blur(20px);
  box-shadow: var(--shadow-lg);
  transition: var(--transition);
}

.stat-item:hover {
  background: var(--card-bg-hover);
  border-color: var(--border-color-hover);
  transform: translateY(-2px);
  box-shadow: var(--shadow-xl);
}

.stat-number {
  font-size: 2.5rem;
  font-weight: 800;
  color: var(--accent-color);
  margin-bottom: var(--spacing-xs);
}

.stat-label {
  font-size: 0.9rem;
  color: var(--text-secondary);
  font-weight: 600;
}



/* Commands Container */
.commands-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--spacing-lg);
  position: relative;
  z-index: 2;
}

/* Liquid Navigation Container */
.liquid-nav-container {
  margin-bottom: var(--spacing-2xl);
  position: relative;
}

.liquid-nav {
  background: var(--card-bg);
  backdrop-filter: blur(20px);
  border: 1px solid var(--border-color);
  border-radius: 60px;
  padding: var(--spacing-md);
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  box-shadow: var(--shadow-sm);
}

.nav-scroll-container {
  flex: 1;
  overflow-x: auto;
  overflow-y: hidden;
  scrollbar-width: none;
  -ms-overflow-style: none;
}

.nav-scroll-container::-webkit-scrollbar {
  display: none;
}

.nav-scroll-track {
  display: flex;
  gap: var(--spacing-sm);
  padding: var(--spacing-xs) 0;
  min-width: max-content;
}

.nav-module-btn {
  background: transparent;
  color: var(--text-secondary);
  padding: var(--spacing-sm) var(--spacing-md);
  border-radius: 8px;
  font-weight: 600;
  font-size: 0.9rem;
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-xs);
  transition: var(--transition);
  border: none;
  cursor: pointer;
  white-space: nowrap;
  min-width: max-content;
}

.nav-module-btn:hover,
.nav-module-btn.active {
  background: rgba(255, 255, 255, 0.1);
  color: var(--text-primary);
}

.module-count {
  background: rgba(255, 255, 255, 0.2);
  padding: 2px 6px;
  border-radius: 8px;
  font-size: 0.7rem;
  font-weight: 700;
  margin-left: var(--spacing-xs);
}

.nav-module-btn.active .module-count {
  background: rgba(255, 255, 255, 0.3);
}

/* Animated Search Bar */
.search-toggle-container {
  display: flex;
  align-items: center;
  position: relative;
}

.animated-search {
  position: relative;
  display: flex;
  align-items: center;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.search-icon-btn {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: var(--card-bg);
  border: 1px solid var(--border-color);
  color: var(--text-primary);
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.9rem;
  backdrop-filter: blur(20px);
  box-shadow: var(--shadow-sm);
  z-index: 2;
}

.search-icon-btn:hover {
  background: var(--card-bg-hover);
  border-color: var(--border-color-hover);
  transform: scale(1.05);
}

.search-input-container {
  position: absolute;
  right: 0;
  top: 0;
  width: 40px;
  height: 40px;
  background: var(--card-bg);
  border: 1px solid var(--border-color);
  border-radius: 60px;
  backdrop-filter: blur(20px);
  box-shadow: var(--shadow-sm);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  overflow: hidden;
  z-index: 1;
}

.search-input-container.expanded {
  width: 300px;
  border-color: var(--accent-color);
  box-shadow: 0 0 0 3px rgba(63, 114, 129, 0.2), var(--shadow-lg);
}

.search-input-field {
  width: 100%;
  height: 100%;
  background: transparent;
  border: none;
  outline: none;
  padding: 0 50px 0 50px;
  color: var(--text-primary);
  font-size: 0.9rem;
  opacity: 0;
  transition: opacity 0.3s ease 0.1s;
}

.search-input-container.expanded .search-input-field {
  opacity: 1;
}

.search-input-field::placeholder {
  color: var(--text-secondary);
}

.search-close-btn {
  position: absolute;
  right: 8px;
  top: 50%;
  transform: translateY(-50%);
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  border: none;
  color: var(--text-secondary);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.8rem;
  opacity: 0;
  transition: all 0.3s ease;
}

.search-input-container.expanded .search-close-btn {
  opacity: 1;
}

.search-close-btn:hover {
  background: rgba(255, 255, 255, 0.2);
  color: var(--text-primary);
}





/* Commands List Section */
.commands-list {
  padding: 60px 0 120px;
  position: relative;
}

.commands-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  gap: var(--spacing-md);
  max-width: 1200px;
  margin: 0 auto;
}

.command-card {
  background: var(--card-bg);
  border: 1px solid var(--border-color);
  border-radius: 35px;
  padding: 0;
  box-shadow: var(--shadow-sm);
  backdrop-filter: blur(20px);
  transition: var(--transition);
  position: relative;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.command-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, rgba(122, 159, 176, 0.4), transparent);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.command-card:hover {
  background: var(--card-bg-hover);
  border-color: var(--border-color-hover);
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.command-card:hover::before {
  opacity: 1;
}

/* No commands state */
.no-commands {
  grid-column: 1 / -1;
  text-align: center;
  padding: var(--spacing-3xl);
  color: var(--text-secondary);
}

.no-commands i {
  font-size: 3rem;
  margin-bottom: var(--spacing-lg);
  opacity: 0.5;
}

.no-commands h3 {
  font-size: 1.5rem;
  margin-bottom: var(--spacing-sm);
  color: var(--text-primary);
}

.no-commands p {
  font-size: 1rem;
  opacity: 0.8;
}

.command-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: rgba(255, 255, 255, 0.05);
  padding: var(--spacing-lg);
  border-bottom: 1px solid var(--border-color);
}

.command-name {
  font-size: 1.3rem;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.copy-btn {
  background: rgba(122, 159, 176, 0.2);
  border: none;
  border-radius: 10px;
  padding: 8px;
  color: var(--text-secondary);
  cursor: pointer;
  transition: var(--transition);
  font-size: 0.85rem;
  border: 1px solid rgba(122, 159, 176, 0.3);
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.copy-btn:hover {
  background: rgba(122, 159, 176, 0.3);
  color: var(--text-primary);
  transform: scale(1.1);
}

.command-description {
  color: var(--text-primary);
  margin: 0;
  line-height: 1.5;
  font-size: 0.95rem;
  padding: var(--spacing-lg);
  flex: 1;
}

.command-details {
  background: rgba(255, 255, 255, 0.03);
  padding: var(--spacing-lg);
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
  border-top: 1px solid var(--border-color);
}

.command-detail {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}

.detail-label {
  color: var(--text-secondary);
  font-size: 0.85rem;
  font-weight: 600;
  margin-bottom: 4px;
  opacity: 0.8;
}

.detail-value {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

/* Alias and Permission Text */
.alias-tag, .permission-tag {
  color: var(--text-primary);
  font-size: 0.9rem;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  font-weight: 500;
  display: inline-block;
}

.no-aliases {
  color: var(--text-secondary);
  font-style: italic;
  font-size: 0.9rem;
  opacity: 0.7;
}

/* Copy Notification */
.copy-notification {
  position: fixed;
  top: 20px;
  right: 20px;
  background: var(--card-bg);
  color: var(--text-primary);
  padding: var(--spacing-md) var(--spacing-lg);
  border-radius: 60px;
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  font-weight: 600;
  font-size: 0.9rem;
  box-shadow: var(--shadow-lg);
  backdrop-filter: blur(20px);
  border: 1px solid var(--border-color);
  transform: translateX(100%);
  opacity: 0;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  z-index: 1000;
}

.copy-notification.show {
  transform: translateX(0);
  opacity: 1;
}

.copy-notification i {
  font-size: 1rem;
}





@keyframes scrollingStars {
  0% { transform: translateY(0px); }
  100% { transform: translateY(-200px); }
}

/* Responsive Design */
@media (max-width: 1024px) {
  .commands-grid {
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  }

  .liquid-nav {
    padding: var(--spacing-sm);
  }

  .nav-module-btn {
    font-size: 0.85rem;
    padding: var(--spacing-xs) var(--spacing-md);
  }
}

@media (max-width: 768px) {
  .commands-grid {
    grid-template-columns: 1fr;
    gap: var(--spacing-sm);
  }

  .commands-title {
    font-size: 2.5rem;
  }

  .liquid-nav {
    flex-direction: column;
    gap: var(--spacing-sm);
  }

  .nav-scroll-container {
    order: 2;
  }

  .search-toggle-container {
    order: 1;
    align-self: flex-end;
  }

  .command-card {
    padding: var(--spacing-md);
  }

  .command-name {
    font-size: 1.1rem;
  }

  .command-details {
    gap: var(--spacing-sm);
  }

  .copy-notification {
    top: 10px;
    right: 10px;
    left: 10px;
    transform: translateY(-100%);
  }

  .copy-notification.show {
    transform: translateY(0);
  }

  /* Mobile search adjustments */
  .search-input-container.expanded {
    width: 250px;
  }

  /* Prevent horizontal overscroll on commands page */
  html, body {
    overflow-x: hidden !important;
    width: 100% !important;
    max-width: 100% !important;
  }

  .commands-container {
    width: 100% !important;
    max-width: 100% !important;
    overflow-x: hidden !important;
    padding-left: var(--spacing-md);
    padding-right: var(--spacing-md);
  }

  /* Optimize mobile touch scrolling for module navigation */
  .nav-scroll-container {
    -webkit-overflow-scrolling: touch;
    touch-action: pan-x;
    scrollbar-width: none;
    -ms-overflow-style: none;
  }

  .nav-scroll-container::-webkit-scrollbar {
    display: none;
  }
}

@media (max-width: 480px) {
  .commands-container {
    padding: 0 var(--spacing-sm);
  }

  .command-card {
    padding: var(--spacing-sm);
  }

  .command-name {
    font-size: 1rem;
  }

  .nav-module-btn {
    font-size: 0.8rem;
    padding: 6px var(--spacing-sm);
  }

  .search-input-field {
    font-size: 0.9rem;
  }

  .search-input-container.expanded {
    width: 200px;
  }
}
