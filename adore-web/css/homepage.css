/* Homepage Specific Styles */

/* Epic Navigation */
.navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  background: transparent;
  z-index: 1000;
  transition: var(--transition);
}





.navbar {
  display: flex;
  align-items: center;
  justify-content: center;
  max-width: 1000px;
  margin: 0 auto;
  padding: 0 var(--spacing-lg);
  height: 80px;
}

.nav-brand {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  font-weight: 800;
  font-size: 1.6rem;
  color: var(--text-primary);
  cursor: pointer;
  transition: var(--transition);
  position: relative;
}

.nav-brand:hover {
  transform: scale(1.05);
}

.brand-icon {
  width: 52px;
  height: 52px;
  background: linear-gradient(45deg, var(--accent-hover), var(--accent-color), var(--accent-hover));
  background-size: 400% 400%;
  border-radius: 26px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.4rem;
  position: relative;
  overflow: hidden;
  animation: continuousGradientFlow 6s linear infinite;
  box-shadow: 0 0 15px rgba(63, 114, 129, 0.3);
}

.brand-icon::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.3), transparent, rgba(255, 255, 255, 0.1), transparent);
  background-size: 200% 200%;
  animation: continuousShine 3s linear infinite;
}

@keyframes continuousGradientFlow {
  0% { background-position: 0% 0%; }
  25% { background-position: 100% 0%; }
  50% { background-position: 100% 100%; }
  75% { background-position: 0% 100%; }
  100% { background-position: 0% 0%; }
}

@keyframes continuousShine {
  0% { background-position: -200% -200%; }
  100% { background-position: 200% 200%; }
}

.bot-avatar {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 26px;
}

.brand-text {
  background: linear-gradient(90deg, #ffffff, #b9bbbe, #ffffff, #e3e5e8, #ffffff);
  background-size: 300% 100%;
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
  animation: continuousTextFlow 4s linear infinite;
}

@keyframes continuousTextFlow {
  0% { background-position: 0% 50%; }
  100% { background-position: 300% 50%; }
}

.nav-links {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  background: var(--card-bg);
  padding: var(--spacing-sm) var(--spacing-lg);
  border-radius: 50px;
  backdrop-filter: blur(20px);
  border: 1px solid var(--border-color);
  width: fit-content;
  box-shadow: var(--shadow-sm);
}

.nav-link {
  color: var(--text-secondary);
  text-decoration: none;
  font-weight: 600;
  font-size: 0.9rem;
  transition: var(--transition);
  position: relative;
  padding: var(--spacing-xs) var(--spacing-md);
  border-radius: 25px;
  white-space: nowrap;
}

.nav-link:hover,
.nav-link.active {
  color: #ffffff;
}



.nav-cta {
  background: linear-gradient(45deg, var(--accent-hover), var(--accent-color), var(--accent-hover));
  background-size: 400% 400%;
  color: white;
  padding: var(--spacing-md) var(--spacing-xl);
  border-radius: 50px;
  font-weight: 700;
  font-size: 0.95rem;
  transition: var(--transition);
  position: relative;
  overflow: hidden;
  border: 1px solid var(--border-color);
  box-shadow: var(--shadow-md);
  animation: continuousCtaFlow 8s linear infinite;
}

.nav-cta::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.4), transparent, rgba(255, 255, 255, 0.2), transparent);
  background-size: 200% 200%;
  animation: continuousCtaShine 2.5s linear infinite;
}

@keyframes continuousCtaFlow {
  0% { background-position: 0% 0%; }
  25% { background-position: 100% 0%; }
  50% { background-position: 100% 100%; }
  75% { background-position: 0% 100%; }
  100% { background-position: 0% 0%; }
}

@keyframes continuousCtaShine {
  0% { background-position: -200% -200%; }
  100% { background-position: 200% 200%; }
}

@keyframes scrollingStars {
  0% { transform: translateY(0px); }
  100% { transform: translateY(-200px); }
}

.nav-cta:hover {
  transform: translateY(-3px) scale(1.05);
  box-shadow: var(--shadow-lg);
  border-color: var(--border-color-hover);
}

.mobile-menu-toggle {
  display: none;
  position: absolute;
  right: var(--spacing-lg);
  top: 50%;
  transform: translateY(-50%);
  color: var(--text-primary);
  font-size: 1.5rem;
  cursor: pointer;
  padding: var(--spacing-sm);
  border-radius: var(--radius-md);
  background: var(--card-bg);
  border: 1px solid var(--border-color);
  transition: var(--transition);
  backdrop-filter: blur(10px);
}

.mobile-menu-toggle:hover {
  background: var(--card-bg-hover);
  border-color: var(--border-color-hover);
  transform: translateY(-50%) scale(1.1);
}





/* Enhanced brand hover effect */
.nav-brand::after {
  content: '';
  position: absolute;
  top: -5px;
  left: -5px;
  right: -5px;
  bottom: -5px;
  background: var(--accent-light);
  border-radius: var(--radius-lg);
  opacity: 0;
  transition: var(--transition);
  z-index: -1;
}

.nav-brand:hover::after {
  opacity: 1;
}



/* CTA button epic effects */
.nav-cta::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.3) 0%, transparent 70%);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  transition: all 0.6s ease;
  pointer-events: none;
}

.nav-cta:hover::after {
  width: 100px;
  height: 100px;
}



/* Epic typing indicator for brand */
.brand-text::after {
  content: '|';
  color: var(--accent-color);
  animation: blink 1s infinite;
  margin-left: 2px;
}

@keyframes blink {
  0%, 50% { opacity: 1; }
  51%, 100% { opacity: 0; }
}

/* Epic Ripple Effect */
.ripple {
  position: absolute;
  border-radius: 50%;
  background: rgba(88, 101, 242, 0.6);
  transform: scale(0);
  animation: ripple-animation 0.6s linear;
  pointer-events: none;
  z-index: 1;
}

@keyframes ripple-animation {
  to {
    transform: scale(4);
    opacity: 0;
  }
}

/* Enhanced brand icon transition */
.brand-icon {
  transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Navbar scroll effects */
.navbar {
  transition: transform 0.3s ease-out, opacity 0.3s ease-out;
}

.navbar.hidden {
  transform: translateY(-100%);
  opacity: 0;
}



/* Cool hover effects for nav elements */
.nav-link, .nav-cta {
  position: relative;
  overflow: hidden;
}

/* Enhanced mobile menu and mobile optimizations */
@media (max-width: 768px) {
  /* Prevent horizontal overscroll */
  html, body {
    overflow-x: hidden !important;
    width: 100% !important;
    max-width: 100% !important;
  }

  /* Optimize mobile scaling */
  .hero-container,
  .commands-container,
  .servers-container,
  .integrations-container {
    width: 100% !important;
    max-width: 100% !important;
    overflow-x: hidden;
    padding-left: var(--spacing-md);
    padding-right: var(--spacing-md);
  }

  .nav-links {
    background: var(--card-bg);
    backdrop-filter: blur(20px);
  }

  .nav-links::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000"><circle cx="100" cy="100" r="0.5" fill="white" opacity="0.1"/><circle cx="300" cy="200" r="0.3" fill="white" opacity="0.08"/><circle cx="500" cy="150" r="0.4" fill="white" opacity="0.12"/><circle cx="700" cy="250" r="0.2" fill="white" opacity="0.06"/><circle cx="900" cy="180" r="0.6" fill="white" opacity="0.1"/></svg>') repeat;
    animation: sparkle 5s linear infinite;
    pointer-events: none;
    z-index: 1;
  }

  .nav-links > * {
    position: relative;
    z-index: 2;
  }
}

/* Global Page Background */
body {
  background: linear-gradient(135deg, #0a0a0a 0%, #111111 25%, #0a0a0a 50%, #111111 75%, #0a0a0a 100%);
  min-height: 100vh;
  position: relative;
  margin: 0;
  padding: 0;
  width: 100%;
  overflow-x: hidden;
}

/* Hero Section Subtle Milky Way Effect */
.hero-section {
  position: relative;
  background: linear-gradient(45deg, transparent 0%, transparent 35%, rgba(255, 255, 255, 0.03) 45%, rgba(173, 216, 230, 0.04) 50%, rgba(255, 255, 255, 0.03) 55%, transparent 65%, transparent 100%);
}





/* Hero Section */
.hero-section {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  background: transparent;
  width: 100%;
  overflow: hidden;
}

.hero-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1;
}









@keyframes continuousStarMovement {
  0% { background-position: 0 0; }
  100% { background-position: 1000px 1000px; }
}

@keyframes continuousTwinkling {
  0% { background-position: 0 0; }
  100% { background-position: -1000px -1000px; }
}

@keyframes continuousDrift {
  0% { background-position: 0 0; }
  100% { background-position: 1000px 0; }
}

@keyframes float {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  33% { transform: translateY(-20px) rotate(1deg); }
  66% { transform: translateY(10px) rotate(-1deg); }
}

.hero-container {
  width: 100%;
  max-width: 1000px;
  margin: 0 auto;
  padding: 0 var(--spacing-lg);
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
  z-index: 2;
  box-sizing: border-box;
}

.hero-content {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);
  text-align: center;
  max-width: 900px;
  width: 100%;
  margin: 0 auto;
  align-items: center;
  justify-content: center;
}

.hero-badge {
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-xs);
  background: rgba(88, 101, 242, 0.1);
  border: 1px solid rgba(88, 101, 242, 0.3);
  padding: var(--spacing-md) var(--spacing-xl);
  border-radius: 50px;
  color: var(--accent-color);
  font-size: 0.9rem;
  font-weight: 600;
  width: fit-content;
  backdrop-filter: blur(10px);
}

.hero-title {
  font-size: 3.5rem;
  font-weight: 700;
  line-height: 1.2;
  color: var(--text-primary);
  margin: 0;
}

.gradient-text {
  background: linear-gradient(135deg, var(--accent-color), #5a9aaa);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
}

.hero-description {
  font-size: 1.1rem;
  color: var(--text-secondary);
  line-height: 1.6;
  max-width: 600px;
  margin: 0 auto;
}

.hero-actions {
  display: flex;
  gap: var(--spacing-md);
  align-items: center;
  justify-content: center;
}

.btn-primary {
  background: linear-gradient(135deg, var(--accent-hover), var(--accent-color));
  color: white;
  padding: var(--spacing-md) var(--spacing-2xl);
  border-radius: 50px;
  font-weight: 700;
  font-size: 1.1rem;
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  transition: var(--transition);
  border: 1px solid rgba(255, 255, 255, 0.2);
  cursor: pointer;
  box-shadow:
    0 8px 25px rgba(63, 114, 129, 0.4),
    inset 0 1px 0 rgba(255, 255, 255, 0.3),
    inset 0 -1px 0 rgba(0, 0, 0, 0.1);
}



.btn-primary:hover {
  transform: translateY(-3px) scale(1.05);
  box-shadow:
    0 12px 35px rgba(63, 114, 129, 0.6),
    inset 0 1px 0 rgba(255, 255, 255, 0.4),
    inset 0 -1px 0 rgba(0, 0, 0, 0.2);
  border-color: rgba(255, 255, 255, 0.3);
}

.btn-secondary {
  background: var(--card-bg);
  color: var(--text-primary);
  padding: var(--spacing-md) var(--spacing-2xl);
  border-radius: 50px;
  font-weight: 700;
  font-size: 1.1rem;
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  transition: var(--transition);
  border: 1px solid var(--border-color);
  cursor: pointer;
  backdrop-filter: blur(10px);
  box-shadow:
    0 8px 25px rgba(0, 0, 0, 0.4),
    inset 0 1px 0 rgba(63, 114, 129, 0.3),
    inset 0 -1px 0 rgba(0, 0, 0, 0.3);
}



.btn-secondary:hover {
  background: var(--card-bg-hover);
  border-color: var(--border-color-hover);
  transform: translateY(-3px) scale(1.05);
  box-shadow:
    0 12px 35px rgba(0, 0, 0, 0.5),
    inset 0 1px 0 rgba(63, 114, 129, 0.4),
    inset 0 -1px 0 rgba(0, 0, 0, 0.4);
}

.hero-stats {
  display: flex;
  gap: var(--spacing-xl);
  margin-top: var(--spacing-lg);
}

/* Epic Cosmic Bot Visualization */
.hero-visual {
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
  height: 600px;
}

.cosmic-bot-container {
  position: relative;
  width: 500px;
  height: 500px;
  display: flex;
  justify-content: center;
  align-items: center;
}

/* Central Bot with Cosmic Rings */
.cosmic-bot-center {
  position: relative;
  z-index: 10;
}

.cosmic-rings {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.cosmic-ring {
  position: absolute;
  border: 3px solid transparent;
  border-radius: 50%;
  background: linear-gradient(45deg, rgba(88, 101, 242, 0.8), rgba(114, 137, 218, 0.6), rgba(88, 101, 242, 0.8));
  background-clip: padding-box;
  animation: rotate 10s linear infinite;
  box-shadow: 0 0 20px rgba(88, 101, 242, 0.4);
}

.ring-1 {
  width: 200px;
  height: 200px;
  margin: -100px 0 0 -100px;
  border-image: linear-gradient(45deg, rgba(88, 101, 242, 0.9), transparent, rgba(88, 101, 242, 0.9)) 1;
  animation-duration: 8s;
}

.ring-2 {
  width: 280px;
  height: 280px;
  margin: -140px 0 0 -140px;
  border-image: linear-gradient(45deg, rgba(114, 137, 218, 0.8), transparent, rgba(114, 137, 218, 0.8)) 1;
  animation-duration: 12s;
  animation-direction: reverse;
}

.ring-3 {
  width: 360px;
  height: 360px;
  margin: -180px 0 0 -180px;
  border-image: linear-gradient(45deg, rgba(88, 101, 242, 0.7), transparent, rgba(88, 101, 242, 0.7)) 1;
  animation-duration: 16s;
}

.bot-avatar-cosmic {
  position: relative;
  width: 120px;
  height: 120px;
  border-radius: 50%;
  overflow: hidden;
  box-shadow:
    0 0 40px rgba(88, 101, 242, 0.6),
    0 0 80px rgba(88, 101, 242, 0.3),
    inset 0 0 20px rgba(255, 255, 255, 0.1);
  animation: cosmicPulse 3s ease-in-out infinite;
}

.cosmic-avatar-img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 50%;
}

.cosmic-glow {
  position: absolute;
  top: -20px;
  left: -20px;
  right: -20px;
  bottom: -20px;
  background: radial-gradient(circle, rgba(88, 101, 242, 0.3) 0%, transparent 70%);
  border-radius: 50%;
  animation: glowPulse 2s ease-in-out infinite alternate;
  z-index: -1;
}

/* Floating Feature Orbs */
.feature-orbs {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
}

.feature-orb {
  position: absolute;
  width: 70px;
  height: 70px;
  background: linear-gradient(135deg, rgba(25, 27, 30, 0.95), rgba(32, 34, 37, 0.9));
  border: 2px solid rgba(88, 101, 242, 0.8);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--accent-color);
  font-size: 1.6rem;
  box-shadow:
    0 0 30px rgba(88, 101, 242, 0.8),
    0 0 60px rgba(88, 101, 242, 0.5);
  cursor: pointer;
  transition: all 0.3s ease;
  animation: orbFloat 4s ease-in-out infinite;
  z-index: 25;
}

.feature-orb::before {
  content: attr(data-feature);
  position: absolute;
  top: -40px;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(0, 0, 0, 0.9);
  color: white;
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 0.75rem;
  font-weight: 600;
  opacity: 0;
  transition: opacity 0.3s ease;
  pointer-events: none;
  white-space: nowrap;
  backdrop-filter: blur(10px);
}

.feature-orb:hover::before {
  opacity: 1;
}

.feature-orb:hover {
  transform: scale(1.2);
  box-shadow:
    0 0 40px rgba(88, 101, 242, 1),
    0 0 80px rgba(88, 101, 242, 0.6);
  border-color: rgba(88, 101, 242, 1);
}

.orb-trail {
  position: absolute;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  background: radial-gradient(circle, rgba(88, 101, 242, 0.2) 0%, transparent 70%);
  animation: trailPulse 2s ease-in-out infinite;
  z-index: -1;
}

/* Orb Positioning */
.orb-1 {
  top: 20%;
  left: 15%;
  animation-delay: 0s;
}

.orb-2 {
  top: 15%;
  right: 20%;
  animation-delay: 1s;
}

.orb-3 {
  bottom: 25%;
  left: 10%;
  animation-delay: 2s;
}

.orb-4 {
  bottom: 20%;
  right: 15%;
  animation-delay: 3s;
}

/* Cosmic Particles */
.cosmic-particles {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  pointer-events: none;
}

.particle {
  position: absolute;
  width: 6px;
  height: 6px;
  background: rgba(88, 101, 242, 0.9);
  border-radius: 50%;
  animation: particleFloat 6s ease-in-out infinite;
  box-shadow: 0 0 15px rgba(88, 101, 242, 0.8);
}

.particle-1 { top: 10%; left: 25%; animation-delay: 0s; }
.particle-2 { top: 30%; right: 30%; animation-delay: 1s; }
.particle-3 { bottom: 40%; left: 20%; animation-delay: 2s; }
.particle-4 { bottom: 15%; right: 25%; animation-delay: 3s; }
.particle-5 { top: 60%; left: 80%; animation-delay: 4s; }
.particle-6 { top: 80%; right: 70%; animation-delay: 5s; }

/* Energy Waves */
.energy-waves {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  pointer-events: none;
}

.energy-wave {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  border: 1px solid rgba(88, 101, 242, 0.3);
  border-radius: 50%;

}

.wave-1 {
  animation-delay: 0s;
}

.wave-2 {
  animation-delay: 1.3s;
}

.wave-3 {
  animation-delay: 2.6s;
}

/* Animations */
@keyframes rotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

@keyframes cosmicPulse {
  0%, 100% {
    transform: scale(1);
    box-shadow:
      0 0 40px rgba(88, 101, 242, 0.6),
      0 0 80px rgba(88, 101, 242, 0.3);
  }
  50% {
    transform: scale(1.05);
    box-shadow:
      0 0 60px rgba(88, 101, 242, 0.8),
      0 0 120px rgba(88, 101, 242, 0.4);
  }
}

@keyframes glowPulse {
  0% { opacity: 0.5; transform: scale(1); }
  100% { opacity: 0.8; transform: scale(1.1); }
}

@keyframes orbFloat {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  25% { transform: translateY(-10px) rotate(90deg); }
  50% { transform: translateY(5px) rotate(180deg); }
  75% { transform: translateY(-5px) rotate(270deg); }
}

@keyframes trailPulse {
  0%, 100% { transform: scale(1); opacity: 0.3; }
  50% { transform: scale(1.5); opacity: 0.1; }
}

@keyframes particleFloat {
  0%, 100% {
    transform: translateY(0px) translateX(0px);
    opacity: 0.8;
  }
  25% {
    transform: translateY(-20px) translateX(10px);
    opacity: 1;
  }
  50% {
    transform: translateY(-10px) translateX(-15px);
    opacity: 0.6;
  }
  75% {
    transform: translateY(-30px) translateX(5px);
    opacity: 0.9;
  }
}



/* Responsive Design for Cosmic Visualization */
@media (max-width: 768px) {
  .hero-container {
    padding: 0 var(--spacing-md);
  }

  .hero-visual {
    height: 400px;
    order: -1;
  }

  .cosmic-bot-container {
    width: 350px;
    height: 350px;
  }

  .ring-1 { width: 140px; height: 140px; margin: -70px 0 0 -70px; }
  .ring-2 { width: 200px; height: 200px; margin: -100px 0 0 -100px; }
  .ring-3 { width: 260px; height: 260px; margin: -130px 0 0 -130px; }

  .bot-avatar-cosmic {
    width: 80px;
    height: 80px;
  }

  .feature-orb {
    width: 45px;
    height: 45px;
    font-size: 1.2rem;
  }
}

/* Bot Showcase Styles */
.hero-visual {
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
}

.bot-showcase {
  width: 100%;
  max-width: 500px;
  position: relative;
}

.discord-interface {
  background: rgba(32, 34, 37, 0.98);
  border-radius: var(--radius-lg);
  padding: var(--spacing-2xl);
  backdrop-filter: blur(25px);
  border: 2px solid rgba(255, 255, 255, 0.2);
  box-shadow:
    0 25px 50px rgba(0, 0, 0, 0.8),
    0 0 0 1px rgba(88, 101, 242, 0.3);
  position: relative;
  overflow: hidden;
}

.discord-interface::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, rgba(88, 101, 242, 0.5), transparent);
  animation: shimmer 3s ease-in-out infinite;
}

.bot-status-card {
  display: flex;
  align-items: center;
  gap: var(--spacing-lg);
  margin-bottom: var(--spacing-2xl);
  padding: var(--spacing-lg);
  background: rgba(25, 27, 30, 0.95);
  border-radius: var(--radius-md);
  border: 2px solid rgba(255, 255, 255, 0.2);
}

.bot-avatar-large {
  position: relative;
  width: 60px;
  height: 60px;
}

.bot-avatar-img {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  object-fit: cover;
}

.status-indicator {
  position: absolute;
  bottom: 2px;
  right: 2px;
  width: 18px;
  height: 18px;
  border-radius: 50%;
  border: 3px solid rgba(54, 57, 63, 1);
}

.status-indicator.online {
  background: #43b581;
  animation: pulse-status 2s ease-in-out infinite;
}

.bot-info h3 {
  color: var(--text-primary);
  margin: 0 0 var(--spacing-xs) 0;
  font-size: 1.3rem;
  font-weight: 700;
}

.bot-status {
  color: var(--text-secondary);
  margin: 0;
  font-size: 0.9rem;
}

/* Bot Showcase Styles */
.hero-visual {
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
}

.bot-showcase {
  width: 100%;
  max-width: 500px;
  position: relative;
}

.discord-interface {
  background: rgba(32, 34, 37, 0.98);
  border-radius: var(--radius-lg);
  padding: var(--spacing-2xl);
  backdrop-filter: blur(25px);
  border: 2px solid rgba(255, 255, 255, 0.2);
  box-shadow:
    0 25px 50px rgba(0, 0, 0, 0.8),
    0 0 0 1px rgba(88, 101, 242, 0.3);
  position: relative;
  overflow: hidden;
}

.discord-interface::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, rgba(88, 101, 242, 0.5), transparent);
  animation: shimmer 3s ease-in-out infinite;
}

.bot-status-card {
  display: flex;
  align-items: center;
  gap: var(--spacing-lg);
  margin-bottom: var(--spacing-2xl);
  padding: var(--spacing-lg);
  background: rgba(25, 27, 30, 0.95);
  border-radius: var(--radius-md);
  border: 2px solid rgba(255, 255, 255, 0.2);
}

.bot-avatar-large {
  position: relative;
  width: 60px;
  height: 60px;
}

.bot-avatar-img {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  object-fit: cover;
}

.status-indicator {
  position: absolute;
  bottom: 2px;
  right: 2px;
  width: 18px;
  height: 18px;
  border-radius: 50%;
  border: 3px solid rgba(54, 57, 63, 1);
}

.status-indicator.online {
  background: #43b581;
  animation: pulse-status 2s ease-in-out infinite;
}

.bot-info h3 {
  color: var(--text-primary);
  margin: 0 0 var(--spacing-xs) 0;
  font-size: 1.3rem;
  font-weight: 700;
}

.bot-status {
  color: var(--text-secondary);
  margin: 0;
  font-size: 0.9rem;
}

.command-demo {
  margin-bottom: var(--spacing-2xl);
}

.command-input {
  background: rgba(64, 68, 75, 0.8);
  padding: var(--spacing-md);
  border-radius: var(--radius-md);
  margin-bottom: var(--spacing-lg);
  font-family: 'Courier New', monospace;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.command-prefix {
  color: var(--accent-color);
  font-weight: 700;
}

.command-text {
  color: var(--text-primary);
  margin-left: var(--spacing-xs);
}

.typing-animation {
  position: relative;
}

.typing-animation::after {
  content: '|';
  color: var(--accent-color);
  animation: blink 1s infinite;
  margin-left: 2px;
}

.homepage .embed-preview {
  background: rgba(47, 49, 54, 0.9);
  border-radius: var(--radius-md);
  border-left: 4px solid var(--accent-color);
  overflow: hidden;
  animation: slideUp 0.5s ease-out;
}

.embed-color-bar {
  height: 4px;
  background: linear-gradient(90deg, var(--accent-color), #7289DA);
  animation: colorShift 3s ease-in-out infinite;
}

.embed-content {
  padding: var(--spacing-lg);
}

.embed-title {
  color: var(--text-primary);
  font-weight: 700;
  font-size: 1.1rem;
  margin-bottom: var(--spacing-sm);
}

.embed-description {
  color: var(--text-secondary);
  margin-bottom: var(--spacing-lg);
  line-height: 1.5;
}

.embed-footer {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  color: var(--text-secondary);
  font-size: 0.8rem;
}

.embed-footer-icon {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  object-fit: cover;
}

.stat-item {
  text-align: center;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 50px;
  padding: var(--spacing-xl) var(--spacing-2xl);
  backdrop-filter: blur(10px);
  transition: var(--transition);
  position: relative;
  overflow: hidden;
}

.stat-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, rgba(88, 101, 242, 0.5), transparent);
  opacity: 0;
  transition: var(--transition);
}

.stat-item:hover::before {
  opacity: 1;
}

.stat-item:hover {
  background: rgba(255, 255, 255, 0.08);
  border-color: rgba(88, 101, 242, 0.3);
  transform: translateY(-3px);
  box-shadow:
    0 10px 25px rgba(0, 0, 0, 0.3),
    0 0 0 1px rgba(88, 101, 242, 0.2);
}

.stat-number {
  font-size: 2.2rem;
  font-weight: 800;
  margin-bottom: var(--spacing-sm);
  background: linear-gradient(135deg, var(--accent-color), #7289DA);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
}

.stat-label {
  font-size: 0.95rem;
  color: var(--text-secondary);
  font-weight: 600;
  letter-spacing: 0.5px;
}

/* Hero Visual */
.hero-visual {
  display: flex;
  justify-content: center;
  align-items: center;
}

.embed-showcase {
  position: relative;
  transform: perspective(1000px) rotateY(-15deg) rotateX(5deg);
  transition: transform 0.3s ease;
}

.embed-showcase:hover {
  transform: perspective(1000px) rotateY(-10deg) rotateX(2deg);
}

.showcase-embed {
  background: #2f3136;
  border-radius: var(--radius-md);
  padding: var(--spacing-lg);
  box-shadow: 
    0 20px 40px rgba(0, 0, 0, 0.5),
    0 0 0 1px rgba(255, 255, 255, 0.1);
  max-width: 400px;
  display: flex;
  gap: var(--spacing-sm);
}

.embed-pill {
  width: 4px;
  background: var(--accent-color);
  border-radius: 2px;
  flex-shrink: 0;
}

.embed-content-demo {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.embed-author-demo {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  font-size: 0.9rem;
  color: var(--text-secondary);
}

.author-icon-demo {
  width: 24px;
  height: 24px;
  border-radius: 50%;
}

.embed-title-demo {
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--text-primary);
}

.embed-description-demo {
  color: var(--text-secondary);
  line-height: 1.4;
}

.embed-fields-demo {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--spacing-sm);
}

.field-demo {
  font-size: 0.9rem;
}

.field-name-demo {
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 2px;
}

.field-value-demo {
  color: var(--text-secondary);
}

.embed-footer-demo {
  font-size: 0.8rem;
  color: var(--text-muted);
  margin-top: var(--spacing-sm);
}

/* Features Section */
.features-section {
  padding: 120px 0;
  background: transparent;
  position: relative;
}



.features-section > * {
  position: relative;
  z-index: 2;
}

.features-container {
  max-width: 1000px;
  margin: 0 auto;
  padding: 0 var(--spacing-lg);
}

.section-header {
  text-align: center;
  margin-bottom: var(--spacing-xl);
}

.section-title {
  font-size: 2.2rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--spacing-md);
  text-shadow: 0 0 20px rgba(88, 101, 242, 0.3);
}

.section-description {
  font-size: 1.2rem;
  color: var(--text-secondary);
  max-width: 600px;
  margin: 0 auto;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: var(--spacing-lg);
  margin-top: var(--spacing-xl);
}

.feature-card {
  background: linear-gradient(135deg, rgba(15, 17, 20, 0.95), rgba(25, 27, 30, 0.9));
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 50px;
  padding: var(--spacing-xl);
  transition: var(--transition);
  position: relative;
  overflow: hidden;
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.4),
    inset 0 1px 0 rgba(255, 255, 255, 0.1),
    inset 0 -1px 0 rgba(0, 0, 0, 0.2);
}

.feature-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  opacity: 1;
  transition: opacity 0.3s ease;
}

.feature-card:hover::before {
  opacity: 1;
}

.feature-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, rgba(63, 114, 129, 0.5), transparent);
  opacity: 0;
  transition: var(--transition);
}

.feature-card:hover {
  transform: translateY(-5px);
  border-color: rgba(255, 255, 255, 0.4);
  background: linear-gradient(135deg, rgba(25, 27, 30, 0.95), rgba(35, 37, 40, 0.9));
  box-shadow:
    0 25px 50px rgba(0, 0, 0, 0.6),
    0 0 0 1px rgba(255, 255, 255, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.2),
    inset 0 -1px 0 rgba(0, 0, 0, 0.3);
}

.feature-card:hover::before {
  opacity: 1;
}

.feature-icon {
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, var(--accent-color), #7289DA);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  color: white;
  margin-bottom: var(--spacing-lg);
}

.feature-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--spacing-md);
}

.feature-description {
  color: var(--text-secondary);
  line-height: 1.6;
}

/* Top Servers Section */
.top-servers-section {
  padding: 120px 0;
  background: transparent;
  position: relative;
}

.servers-container {
  width: 100%;
  margin: 0;
  padding: 0;
  position: relative;
  z-index: 2;
}

.servers-scroll-container {
  margin-top: var(--spacing-xl);
  overflow: hidden;
  position: relative;
  padding: 30px 0;
  margin: var(--spacing-xl) -30px 0 -30px;
}

/* Mobile touch scrolling for servers */
@media (max-width: 768px) {
  .servers-scroll-container {
    overflow-x: auto;
    overflow-y: hidden;
    -webkit-overflow-scrolling: touch;
    scrollbar-width: none;
    -ms-overflow-style: none;
    cursor: grab;
  }

  .servers-scroll-container::-webkit-scrollbar {
    display: none;
  }

  .servers-scroll-container:active {
    cursor: grabbing;
  }
}

/* Removed fade effects for full width */

.servers-track {
  display: flex;
  gap: var(--spacing-lg);
  animation: scrollRightToLeft 40s linear infinite;
  width: fit-content;
  /* Ensure animation is never paused or modified */
  animation-play-state: running !important;
  animation-timing-function: linear !important;
  animation-duration: 40s !important;
}

/* Mobile: Disable animation and enable touch scrolling */
@media (max-width: 768px) {
  .servers-track {
    animation: none !important;
    width: max-content;
    min-width: 100%;
    touch-action: pan-x;
    user-select: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
  }
}

@keyframes scrollRightToLeft {
  0% { transform: translateX(0); }
  100% { transform: translateX(-50%); }
}

.server-card {
  background: linear-gradient(135deg, rgba(15, 17, 20, 0.95), rgba(25, 27, 30, 0.9));
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 50px;
  padding: var(--spacing-xl) var(--spacing-2xl);
  display: flex;
  align-items: center;
  gap: var(--spacing-lg);
  min-width: 280px;
  backdrop-filter: blur(20px);
  transition: var(--transition);
  position: relative;
  overflow: hidden;
  box-shadow:
    0 4px 15px rgba(0, 0, 0, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.15),
    inset 0 -1px 0 rgba(0, 0, 0, 0.2);
}

.server-card:hover {
  transform: translateY(-5px) scale(1.02);
  border-color: rgba(255, 255, 255, 0.4);
}

.server-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  object-fit: cover;
  border: 2px solid rgba(88, 101, 242, 0.3);
  transition: var(--transition);
}

.server-card:hover .server-icon {
  border-color: rgba(88, 101, 242, 0.6);
  box-shadow: 0 0 20px rgba(88, 101, 242, 0.4);
}

.server-info {
  flex: 1;
}

.server-name {
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--spacing-xs);
}

.server-members {
  font-size: 0.9rem;
  color: var(--text-secondary);
}

/* Platform Integrations Section */
.integrations-section {
  padding: 120px 0;
  background: transparent;
  position: relative;
}

.integrations-container {
  width: 100%;
  margin: 0;
  padding: 0;
  position: relative;
  z-index: 2;
}

.integrations-scroll-container {
  margin-top: var(--spacing-xl);
  overflow: hidden;
  position: relative;
  padding: 40px 0;
  margin: var(--spacing-xl) -40px 0 -40px;
}

/* Removed fade effects for full width */

.integrations-track {
  display: flex;
  gap: var(--spacing-xl);
  animation: scrollLeftToRight 40s linear infinite;
  width: fit-content;
  /* Ensure animation is never paused or modified */
  animation-play-state: running !important;
  animation-timing-function: linear !important;
  animation-duration: 40s !important;
}

@keyframes scrollLeftToRight {
  0% { transform: translateX(-50%); }
  100% { transform: translateX(0); }
}

.integration-card {
  background: linear-gradient(135deg, rgba(15, 17, 20, 0.95), rgba(25, 27, 30, 0.9));
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 50px;
  padding: var(--spacing-2xl);
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  gap: var(--spacing-xl);
  min-width: 400px;
  max-width: 450px;
  height: 240px;
  backdrop-filter: blur(20px);
  transition: var(--transition);
  position: relative;
  overflow: hidden;
  box-shadow:
    0 4px 15px rgba(0, 0, 0, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.15),
    inset 0 -1px 0 rgba(0, 0, 0, 0.2);
}

.integration-card:hover {
  transform: translateY(-8px) scale(1.02);
  border-color: rgba(255, 255, 255, 0.4);
}

.integration-icon {
  width: 70px;
  height: 70px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 2.5rem;
  color: white;
  position: relative;
  overflow: hidden;
  transition: var(--transition);
  flex-shrink: 0;
}

.integration-icon.spotify {
  background: linear-gradient(135deg, #1DB954, #1ed760);
  box-shadow: 0 0 30px rgba(29, 185, 84, 0.4);
}

.integration-icon.twitter {
  background: linear-gradient(135deg, #000000, #333333);
  box-shadow: 0 0 30px rgba(0, 0, 0, 0.4);
}

.integration-icon.soundcloud {
  background: linear-gradient(135deg, #ff5500, #ff7700);
  box-shadow: 0 0 30px rgba(255, 85, 0, 0.4);
}

.integration-icon.instagram {
  background: linear-gradient(135deg, #E4405F, #C13584, #833AB4);
  box-shadow: 0 0 30px rgba(228, 64, 95, 0.4);
}

.integration-icon.pinterest {
  background: linear-gradient(135deg, #BD081C, #E60023);
  box-shadow: 0 0 30px rgba(189, 8, 28, 0.4);
}

.integration-icon.tiktok {
  background: linear-gradient(135deg, #000000, #ff0050);
  box-shadow: 0 0 30px rgba(255, 0, 80, 0.4);
}

.integration-icon.lastfm {
  background: linear-gradient(135deg, #D51007, #ff1a1a);
  box-shadow: 0 0 30px rgba(213, 16, 7, 0.4);
}

.integration-card:hover .integration-icon {
  transform: scale(1.05);
}

.integration-header {
  display: flex;
  align-items: center;
  gap: var(--spacing-lg);
}

.integration-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
  min-height: 0;
}

.integration-name {
  font-size: 1.4rem;
  font-weight: 700;
  color: var(--text-primary);
  margin: 0;
}

.integration-description {
  font-size: 1rem;
  color: var(--text-secondary);
  line-height: 1.6;
  opacity: 0.8;
  transition: var(--transition);
  margin: 0;
  padding-bottom: var(--spacing-2xl);
}

.integration-card:hover .integration-description {
  opacity: 1;
  color: var(--text-primary);
}

/* Voicemaster Section */
.voicemaster-section {
  padding: 120px 0;
  background: transparent;
  position: relative;
}

.voicemaster-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: transparent url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000"><circle cx="100" cy="100" r="1" fill="white" opacity="0.8"/><circle cx="200" cy="150" r="0.5" fill="white" opacity="0.6"/><circle cx="300" cy="80" r="1.5" fill="white" opacity="0.9"/><circle cx="450" cy="200" r="0.8" fill="white" opacity="0.7"/><circle cx="600" cy="120" r="1.2" fill="white" opacity="0.8"/><circle cx="750" cy="180" r="0.6" fill="white" opacity="0.5"/><circle cx="850" cy="90" r="1" fill="white" opacity="0.9"/><circle cx="950" cy="160" r="0.7" fill="white" opacity="0.6"/><circle cx="50" cy="250" r="1.3" fill="white" opacity="0.8"/><circle cx="150" cy="300" r="0.9" fill="white" opacity="0.7"/><circle cx="250" cy="280" r="0.4" fill="white" opacity="0.5"/><circle cx="350" cy="320" r="1.1" fill="white" opacity="0.8"/><circle cx="500" cy="350" r="0.6" fill="white" opacity="0.6"/><circle cx="650" cy="300" r="1.4" fill="white" opacity="0.9"/><circle cx="800" cy="330" r="0.8" fill="white" opacity="0.7"/><circle cx="900" cy="280" r="0.5" fill="white" opacity="0.5"/><circle cx="80" cy="400" r="1" fill="white" opacity="0.8"/><circle cx="180" cy="450" r="0.7" fill="white" opacity="0.6"/><circle cx="320" cy="420" r="1.2" fill="white" opacity="0.8"/><circle cx="480" cy="480" r="0.9" fill="white" opacity="0.7"/><circle cx="620" cy="440" r="0.6" fill="white" opacity="0.5"/><circle cx="780" cy="460" r="1.3" fill="white" opacity="0.9"/><circle cx="880" cy="420" r="0.8" fill="white" opacity="0.7"/><circle cx="120" cy="550" r="0.5" fill="white" opacity="0.5"/><circle cx="270" cy="580" r="1.1" fill="white" opacity="0.8"/><circle cx="420" cy="560" r="0.7" fill="white" opacity="0.6"/><circle cx="570" cy="590" r="1.4" fill="white" opacity="0.9"/><circle cx="720" cy="570" r="0.9" fill="white" opacity="0.7"/><circle cx="870" cy="550" r="0.6" fill="white" opacity="0.6"/></svg>') repeat;
  background-size: 1000px 1000px;
  z-index: 1;
  pointer-events: none;
}

.voicemaster-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000"><circle cx="250" cy="200" r="0.6" fill="white" opacity="0.2"/><circle cx="450" cy="300" r="0.4" fill="white" opacity="0.15"/><circle cx="650" cy="250" r="0.5" fill="white" opacity="0.25"/><circle cx="850" cy="350" r="0.3" fill="white" opacity="0.1"/></svg>') repeat;
  background-size: 1000px 1000px;
  animation: continuousStarField 30s linear infinite;
  z-index: 1;
}

.voicemaster-container {
  max-width: 1000px;
  margin: 0 auto;
  padding: 0 var(--spacing-lg);
  position: relative;
  z-index: 2;
}

.voicemaster-grid {
  display: grid;
  grid-template-columns: 2fr 1fr;
  grid-template-rows: auto auto;
  gap: var(--spacing-xl);
  margin-top: var(--spacing-xl);
}

.voicemaster-card:nth-child(1) {
  grid-column: 1 / 2;
  grid-row: 1 / 2;
}

.voicemaster-card:nth-child(2) {
  grid-column: 2 / 3;
  grid-row: 1 / 3;
  flex-direction: column;
  text-align: center;
}

.voicemaster-card:nth-child(3) {
  grid-column: 1 / 2;
  grid-row: 2 / 3;
}

.voicemaster-card {
  background: linear-gradient(135deg, rgba(15, 17, 20, 0.95), rgba(25, 27, 30, 0.9));
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 50px;
  padding: var(--spacing-2xl);
  display: flex;
  gap: var(--spacing-xl);
  backdrop-filter: blur(20px);
  transition: var(--transition);
  position: relative;
  overflow: hidden;
  min-height: 200px;
  box-shadow:
    0 8px 25px rgba(0, 0, 0, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.15),
    inset 0 -1px 0 rgba(0, 0, 0, 0.2);
}

.voicemaster-card:nth-child(2) {
  min-height: 420px;
  justify-content: center;
  align-items: center;
}

.voicemaster-card:hover {
  transform: translateY(-5px) scale(1.02);
  border-color: rgba(255, 255, 255, 0.4);
}

.voicemaster-icon {
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, var(--accent-color), #5a9aaa);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 2.5rem;
  color: white;
  flex-shrink: 0;
  position: relative;
  overflow: hidden;
  box-shadow: 0 0 30px rgba(63, 114, 129, 0.4);
}

/* Removed white shimmer effect from voicemaster icons */

.voicemaster-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

.voicemaster-title {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--text-primary);
  margin: 0;
}

.voicemaster-description {
  font-size: 1.1rem;
  color: var(--text-secondary);
  line-height: 1.6;
  margin: 0;
  opacity: 0.8;
  transition: var(--transition);
}

.voicemaster-card:hover .voicemaster-description {
  opacity: 1;
  color: var(--text-primary);
}

.voicemaster-visual {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 200px;
}

/* Voice Channel Demo */
.vc-demo {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
  background: rgba(10, 12, 15, 0.95);
  padding: var(--spacing-lg);
  border-radius: 30px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.vc-channel {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-sm) var(--spacing-md);
  border-radius: 20px;
  background: rgba(0, 0, 0, 0.4);
  transition: var(--transition);
  position: relative;
}

.vc-channel.active {
  background: rgba(88, 101, 242, 0.2);
  border: 1px solid rgba(88, 101, 242, 0.4);
}

.vc-channel i {
  color: var(--text-secondary);
  font-size: 0.9rem;
}

.vc-channel.active i {
  color: var(--accent-color);
}

.vc-channel span {
  flex: 1;
  color: var(--text-secondary);
  font-size: 0.9rem;
}

.vc-channel.active span {
  color: var(--text-primary);
}

.vc-users {
  background: rgba(88, 101, 242, 0.8);
  color: white;
  padding: 2px 6px;
  border-radius: 15px;
  font-size: 0.8rem;
  font-weight: 600;
}

/* Interface Demo */
.interface-demo {
  background: rgba(10, 12, 15, 0.95);
  border-radius: 30px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  overflow: hidden;
}

.interface-header {
  background: rgba(88, 101, 242, 0.2);
  padding: var(--spacing-md);
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.interface-header i {
  color: var(--accent-color);
}

.interface-header span {
  color: var(--text-primary);
  font-weight: 600;
}

.interface-controls {
  padding: var(--spacing-lg);
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--spacing-sm);
}

.control-btn {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-sm);
  background: rgba(0, 0, 0, 0.4);
  border-radius: 20px;
  transition: var(--transition);
  cursor: pointer;
}

.control-btn:hover {
  background: rgba(88, 101, 242, 0.2);
  transform: translateY(-2px);
}

.control-btn i {
  color: var(--accent-color);
  font-size: 0.9rem;
}

.control-btn span {
  color: var(--text-secondary);
  font-size: 0.9rem;
}

/* Setup Demo */
.setup-demo {
  background: rgba(10, 12, 15, 0.95);
  padding: var(--spacing-lg);
  border-radius: 30px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.command-line {
  background: rgba(0, 0, 0, 0.8);
  padding: var(--spacing-md);
  border-radius: 20px;
  margin-bottom: var(--spacing-lg);
  font-family: 'Courier New', monospace;
}

.command-prefix {
  color: var(--accent-color);
  font-weight: 700;
}

.command-text {
  color: var(--text-primary);
  margin-left: var(--spacing-sm);
}

.setup-steps {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.step {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-sm);
  border-radius: 20px;
  transition: var(--transition);
}

.step.completed {
  background: rgba(0, 0, 0, 0.3);
}

.step i {
  color: var(--green);
  font-size: 0.9rem;
}

.step span {
  color: var(--text-secondary);
  font-size: 0.9rem;
}

.step.completed span {
  color: var(--text-primary);
}



/* Music Features Section */
.music-section {
  padding: 120px 0;
  background: transparent;
  position: relative;
}

.music-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: transparent url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000"><circle cx="100" cy="100" r="1" fill="white" opacity="0.8"/><circle cx="200" cy="150" r="0.5" fill="white" opacity="0.6"/><circle cx="300" cy="80" r="1.5" fill="white" opacity="0.9"/><circle cx="450" cy="200" r="0.8" fill="white" opacity="0.7"/><circle cx="600" cy="120" r="1.2" fill="white" opacity="0.8"/><circle cx="750" cy="180" r="0.6" fill="white" opacity="0.5"/><circle cx="850" cy="90" r="1" fill="white" opacity="0.9"/><circle cx="950" cy="160" r="0.7" fill="white" opacity="0.6"/><circle cx="50" cy="250" r="1.3" fill="white" opacity="0.8"/><circle cx="150" cy="300" r="0.9" fill="white" opacity="0.7"/><circle cx="250" cy="280" r="0.4" fill="white" opacity="0.5"/><circle cx="350" cy="320" r="1.1" fill="white" opacity="0.8"/><circle cx="500" cy="350" r="0.6" fill="white" opacity="0.6"/><circle cx="650" cy="300" r="1.4" fill="white" opacity="0.9"/><circle cx="800" cy="330" r="0.8" fill="white" opacity="0.7"/><circle cx="900" cy="280" r="0.5" fill="white" opacity="0.5"/><circle cx="80" cy="400" r="1" fill="white" opacity="0.8"/><circle cx="180" cy="450" r="0.7" fill="white" opacity="0.6"/><circle cx="320" cy="420" r="1.2" fill="white" opacity="0.8"/><circle cx="480" cy="480" r="0.9" fill="white" opacity="0.7"/><circle cx="620" cy="440" r="0.6" fill="white" opacity="0.5"/><circle cx="780" cy="460" r="1.3" fill="white" opacity="0.9"/><circle cx="880" cy="420" r="0.8" fill="white" opacity="0.7"/><circle cx="120" cy="550" r="0.5" fill="white" opacity="0.5"/><circle cx="270" cy="580" r="1.1" fill="white" opacity="0.8"/><circle cx="420" cy="560" r="0.7" fill="white" opacity="0.6"/><circle cx="570" cy="590" r="1.4" fill="white" opacity="0.9"/><circle cx="720" cy="570" r="0.9" fill="white" opacity="0.7"/><circle cx="870" cy="550" r="0.6" fill="white" opacity="0.6"/></svg>') repeat;
  background-size: 1000px 1000px;
  z-index: 1;
  pointer-events: none;
}

.music-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000"><circle cx="300" cy="250" r="0.5" fill="white" opacity="0.2"/><circle cx="500" cy="350" r="0.3" fill="white" opacity="0.15"/><circle cx="700" cy="300" r="0.4" fill="white" opacity="0.25"/><circle cx="900" cy="400" r="0.2" fill="white" opacity="0.1"/></svg>') repeat;
  background-size: 1000px 1000px;
  animation: continuousStarField 35s linear infinite reverse;
  z-index: 1;
}

.music-container {
  max-width: 1000px;
  margin: 0 auto;
  padding: 0 var(--spacing-lg);
  position: relative;
  z-index: 2;
}

.music-grid {
  display: grid;
  grid-template-columns: 1fr 2fr;
  grid-template-rows: auto auto;
  gap: var(--spacing-xl);
  margin-top: var(--spacing-xl);
}

.music-card:nth-child(1) {
  grid-column: 1 / 2;
  grid-row: 1 / 3;
  flex-direction: column;
  text-align: center;
}

.music-card:nth-child(2) {
  grid-column: 2 / 3;
  grid-row: 1 / 2;
}

.music-card:nth-child(3) {
  grid-column: 2 / 3;
  grid-row: 2 / 3;
}

.music-card {
  background: rgba(15, 17, 20, 0.9);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 50px;
  padding: var(--spacing-2xl);
  display: flex;
  gap: var(--spacing-xl);
  backdrop-filter: blur(20px);
  transition: var(--transition);
  position: relative;
  overflow: hidden;
  min-height: 200px;
}

.music-card:nth-child(1) {
  min-height: 420px;
  justify-content: center;
  align-items: center;
}

.music-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg, transparent, rgba(29, 185, 84, 0.5), transparent);
  opacity: 0;
  transition: var(--transition);
}

.music-card:hover {
  transform: translateY(-5px);
  border-color: rgba(29, 185, 84, 0.3);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.music-card:hover::before {
  opacity: 1;
}

.music-icon {
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, #1DB954, #1ed760);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 2.5rem;
  color: white;
  flex-shrink: 0;
  position: relative;
  overflow: hidden;
  box-shadow: 0 0 30px rgba(29, 185, 84, 0.4);
}

.music-icon.spotify-icon {
  background: linear-gradient(135deg, #1DB954, #1ed760);
  box-shadow: 0 0 30px rgba(29, 185, 84, 0.6);
}

.music-icon::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transition: left 0.6s ease;
}

.music-card:hover .music-icon::before {
  left: 100%;
}

.music-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

.music-title {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--text-primary);
  margin: 0;
}

.music-description {
  font-size: 1.1rem;
  color: var(--text-secondary);
  line-height: 1.6;
  margin: 0;
  opacity: 0.8;
  transition: var(--transition);
}

.music-card:hover .music-description {
  opacity: 1;
  color: var(--text-primary);
}

.music-visual {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 250px;
}

/* Filters Demo */
.filters-demo {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
  background: rgba(10, 12, 15, 0.95);
  padding: var(--spacing-lg);
  border-radius: 30px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  min-width: 200px;
}

.filter-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  padding: var(--spacing-md);
  border-radius: 20px;
  background: rgba(0, 0, 0, 0.4);
  transition: var(--transition);
  cursor: pointer;
}

.filter-item.active {
  background: rgba(29, 185, 84, 0.2);
  border: 1px solid rgba(29, 185, 84, 0.4);
}

.filter-item:hover {
  background: rgba(29, 185, 84, 0.1);
  transform: translateX(5px);
}

.filter-icon {
  width: 30px;
  height: 30px;
  background: rgba(29, 185, 84, 0.8);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 0.8rem;
}

.filter-item span {
  flex: 1;
  color: var(--text-secondary);
  font-size: 0.9rem;
  font-weight: 500;
}

.filter-item.active span {
  color: var(--text-primary);
}

.filter-indicator {
  width: 8px;
  height: 8px;
  background: rgba(29, 185, 84, 0.5);
  border-radius: 50%;
  transition: var(--transition);
}

.filter-item.active .filter-indicator {
  background: #1DB954;
  box-shadow: 0 0 10px rgba(29, 185, 84, 0.8);
  animation: pulse 2s ease-in-out infinite;
}

/* Queue Demo */
.queue-demo {
  background: rgba(10, 12, 15, 0.95);
  border-radius: 30px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  overflow: hidden;
  min-width: 280px;
}

.queue-header {
  background: rgba(29, 185, 84, 0.2);
  padding: var(--spacing-md);
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.queue-header i {
  color: #1DB954;
}

.queue-header span {
  color: var(--text-primary);
  font-weight: 600;
}

.current-track {
  padding: var(--spacing-lg);
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.track-artwork {
  width: 50px;
  height: 50px;
  background: linear-gradient(135deg, #1DB954, #1ed760);
  border-radius: 15px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.2rem;
}

.track-info {
  flex: 1;
}

.track-name {
  color: var(--text-primary);
  font-weight: 600;
  font-size: 0.95rem;
}

.track-artist {
  color: var(--text-secondary);
  font-size: 0.85rem;
}

.track-duration {
  color: var(--text-secondary);
  font-size: 0.85rem;
}

.queue-list {
  padding: var(--spacing-md);
}

.queue-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  padding: var(--spacing-sm);
  border-radius: 20px;
  transition: var(--transition);
}

.queue-item:hover {
  background: rgba(255, 255, 255, 0.05);
}

.queue-number {
  color: var(--text-secondary);
  font-size: 0.8rem;
  width: 20px;
  text-align: center;
}

.queue-track {
  flex: 1;
  color: var(--text-secondary);
  font-size: 0.85rem;
}

.queue-time {
  color: var(--text-muted);
  font-size: 0.8rem;
}

/* Spotify Demo */
.spotify-demo {
  background: rgba(10, 12, 15, 0.95);
  border-radius: 30px;
  border: 1px solid rgba(29, 185, 84, 0.3);
  overflow: hidden;
  min-width: 280px;
}

.spotify-header {
  background: linear-gradient(135deg, rgba(29, 185, 84, 0.3), rgba(30, 215, 96, 0.2));
  padding: var(--spacing-md);
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  border-bottom: 1px solid rgba(29, 185, 84, 0.3);
}

.spotify-logo {
  width: 30px;
  height: 30px;
  background: #1DB954;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1rem;
}

.spotify-header span {
  color: var(--text-primary);
  font-weight: 600;
}

.spotify-player {
  padding: var(--spacing-lg);
}

.spotify-track {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-lg);
}

.spotify-artwork {
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, #1DB954, #1ed760);
  border-radius: 15px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.5rem;
  box-shadow: 0 4px 12px rgba(29, 185, 84, 0.3);
}

.spotify-info {
  flex: 1;
}

.spotify-name {
  color: var(--text-primary);
  font-weight: 600;
  font-size: 1rem;
  margin-bottom: var(--spacing-xs);
}

.spotify-artist {
  color: var(--text-secondary);
  font-size: 0.9rem;
}

.spotify-controls {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: var(--spacing-lg);
  margin-bottom: var(--spacing-lg);
}

.spotify-btn {
  width: 40px;
  height: 40px;
  background: rgba(0, 0, 0, 0.4);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--text-secondary);
  cursor: pointer;
  transition: var(--transition);
}

.spotify-btn.play {
  width: 50px;
  height: 50px;
  background: #1DB954;
  color: white;
  box-shadow: 0 4px 12px rgba(29, 185, 84, 0.4);
}

.spotify-btn:hover {
  background: rgba(29, 185, 84, 0.2);
  color: var(--text-primary);
  transform: scale(1.1);
}

.spotify-btn.play:hover {
  background: #1ed760;
  transform: scale(1.1);
}

.spotify-progress {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.progress-bar {
  height: 4px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 10px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  width: 45%;
  background: #1DB954;
  border-radius: 2px;
  animation: progressPulse 3s ease-in-out infinite;
}

@keyframes progressPulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.7; }
}

.progress-time {
  display: flex;
  justify-content: space-between;
  color: var(--text-muted);
  font-size: 0.8rem;
}

/* Powerful Features Section */
.powerful-features-section {
  padding: 120px 0;
  background: transparent;
  position: relative;
}

.powerful-features-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: transparent url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000"><circle cx="100" cy="100" r="1" fill="white" opacity="0.8"/><circle cx="200" cy="150" r="0.5" fill="white" opacity="0.6"/><circle cx="300" cy="80" r="1.5" fill="white" opacity="0.9"/><circle cx="450" cy="200" r="0.8" fill="white" opacity="0.7"/><circle cx="600" cy="120" r="1.2" fill="white" opacity="0.8"/><circle cx="750" cy="180" r="0.6" fill="white" opacity="0.5"/><circle cx="850" cy="90" r="1" fill="white" opacity="0.9"/><circle cx="950" cy="160" r="0.7" fill="white" opacity="0.6"/><circle cx="50" cy="250" r="1.3" fill="white" opacity="0.8"/><circle cx="150" cy="300" r="0.9" fill="white" opacity="0.7"/><circle cx="250" cy="280" r="0.4" fill="white" opacity="0.5"/><circle cx="350" cy="320" r="1.1" fill="white" opacity="0.8"/><circle cx="500" cy="350" r="0.6" fill="white" opacity="0.6"/><circle cx="650" cy="300" r="1.4" fill="white" opacity="0.9"/><circle cx="800" cy="330" r="0.8" fill="white" opacity="0.7"/><circle cx="900" cy="280" r="0.5" fill="white" opacity="0.5"/><circle cx="80" cy="400" r="1" fill="white" opacity="0.8"/><circle cx="180" cy="450" r="0.7" fill="white" opacity="0.6"/><circle cx="320" cy="420" r="1.2" fill="white" opacity="0.8"/><circle cx="480" cy="480" r="0.9" fill="white" opacity="0.7"/><circle cx="620" cy="440" r="0.6" fill="white" opacity="0.5"/><circle cx="780" cy="460" r="1.3" fill="white" opacity="0.9"/><circle cx="880" cy="420" r="0.8" fill="white" opacity="0.7"/><circle cx="120" cy="550" r="0.5" fill="white" opacity="0.5"/><circle cx="270" cy="580" r="1.1" fill="white" opacity="0.8"/><circle cx="420" cy="560" r="0.7" fill="white" opacity="0.6"/><circle cx="570" cy="590" r="1.4" fill="white" opacity="0.9"/><circle cx="720" cy="570" r="0.9" fill="white" opacity="0.7"/><circle cx="870" cy="550" r="0.6" fill="white" opacity="0.6"/></svg>') repeat;
  background-size: 1000px 1000px;
  z-index: 1;
  pointer-events: none;
}

.powerful-features-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000"><circle cx="200" cy="200" r="0.4" fill="white" opacity="0.2"/><circle cx="400" cy="300" r="0.6" fill="white" opacity="0.15"/><circle cx="600" cy="250" r="0.3" fill="white" opacity="0.25"/><circle cx="800" cy="350" r="0.5" fill="white" opacity="0.1"/><circle cx="300" cy="500" r="0.4" fill="white" opacity="0.18"/><circle cx="700" cy="450" r="0.3" fill="white" opacity="0.22"/></svg>') repeat;
  background-size: 1000px 1000px;
  animation: continuousStarField 40s linear infinite;
  z-index: 1;
}

.powerful-features-container {
  max-width: 1000px;
  margin: 0 auto;
  padding: 0 var(--spacing-lg);
  position: relative;
  z-index: 2;
}

.powerful-features-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  grid-template-rows: repeat(2, auto);
  gap: var(--spacing-xl);
  margin-top: var(--spacing-xl);
}

.powerful-feature-card.large-card {
  grid-column: span 2;
}

.powerful-feature-card:nth-child(1) {
  grid-column: 1 / 3;
  grid-row: 1;
}

.powerful-feature-card:nth-child(2) {
  grid-column: 3;
  grid-row: 1;
}

.powerful-feature-card:nth-child(3) {
  grid-column: 4;
  grid-row: 1;
}

.powerful-feature-card:nth-child(4) {
  grid-column: 1;
  grid-row: 2;
}

.powerful-feature-card:nth-child(5) {
  grid-column: 2 / 4;
  grid-row: 2;
}

.powerful-feature-card:nth-child(6) {
  grid-column: 4;
  grid-row: 2;
}

.powerful-feature-card:nth-child(7) {
  grid-column: 1;
  grid-row: 3;
}

.powerful-feature-card:nth-child(8) {
  grid-column: 2;
  grid-row: 3;
}

.powerful-feature-card {
  background: linear-gradient(135deg, rgba(15, 17, 20, 0.98), rgba(25, 27, 30, 0.95));
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 50px;
  padding: var(--spacing-2xl);
  text-align: center;
  backdrop-filter: blur(20px);
  transition: var(--transition);
  position: relative;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-lg);
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.5),
    inset 0 1px 0 rgba(255, 255, 255, 0.1),
    inset 0 -1px 0 rgba(0, 0, 0, 0.2);
}

.powerful-feature-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  opacity: 1;
  transition: opacity 0.3s ease;
}

.powerful-feature-card:hover::before {
  opacity: 1;
}

.powerful-feature-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg, transparent, rgba(63, 114, 129, 0.5), transparent);
  opacity: 0;
  transition: var(--transition);
}

.powerful-feature-card:hover {
  transform: translateY(-8px);
  border-color: rgba(63, 114, 129, 0.3);
  box-shadow: 0 25px 50px rgba(63, 114, 129, 0.2);
}

.powerful-feature-card:hover::before {
  opacity: 1;
}

.powerful-feature-icon {
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, var(--accent-color), #7289DA);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 2.5rem;
  color: white;
  position: relative;
  overflow: hidden;
  box-shadow: 0 0 30px rgba(63, 114, 129, 0.4);
  transition: var(--transition);
}

.powerful-feature-icon::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transition: left 0.6s ease;
}

.powerful-feature-card:hover .powerful-feature-icon {
  transform: scale(1.1) rotate(5deg);
  box-shadow: 0 0 40px rgba(63, 114, 129, 0.6);
}

.powerful-feature-card:hover .powerful-feature-icon::before {
  left: 100%;
}

.powerful-feature-title {
  font-size: 1.4rem;
  font-weight: 700;
  color: var(--text-primary);
  margin: 0;
  transition: var(--transition);
}

.powerful-feature-card:hover .powerful-feature-title {
  color: var(--accent-color);
}

.powerful-feature-description {
  font-size: 1rem;
  color: var(--text-secondary);
  line-height: 1.6;
  margin: 0;
  opacity: 0.8;
  transition: var(--transition);
}

.powerful-feature-card:hover .powerful-feature-description {
  opacity: 1;
  color: var(--text-primary);
}

/* Reviews Section */
.reviews-section {
  padding: 120px 0;
  background: transparent;
  position: relative;
}

.reviews-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: transparent url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000"><circle cx="100" cy="100" r="1" fill="white" opacity="0.8"/><circle cx="200" cy="150" r="0.5" fill="white" opacity="0.6"/><circle cx="300" cy="80" r="1.5" fill="white" opacity="0.9"/><circle cx="450" cy="200" r="0.8" fill="white" opacity="0.7"/><circle cx="600" cy="120" r="1.2" fill="white" opacity="0.8"/><circle cx="750" cy="180" r="0.6" fill="white" opacity="0.5"/><circle cx="850" cy="90" r="1" fill="white" opacity="0.9"/><circle cx="950" cy="160" r="0.7" fill="white" opacity="0.6"/><circle cx="50" cy="250" r="1.3" fill="white" opacity="0.8"/><circle cx="150" cy="300" r="0.9" fill="white" opacity="0.7"/><circle cx="250" cy="280" r="0.4" fill="white" opacity="0.5"/><circle cx="350" cy="320" r="1.1" fill="white" opacity="0.8"/><circle cx="500" cy="350" r="0.6" fill="white" opacity="0.6"/><circle cx="650" cy="300" r="1.4" fill="white" opacity="0.9"/><circle cx="800" cy="330" r="0.8" fill="white" opacity="0.7"/><circle cx="900" cy="280" r="0.5" fill="white" opacity="0.5"/><circle cx="80" cy="400" r="1" fill="white" opacity="0.8"/><circle cx="180" cy="450" r="0.7" fill="white" opacity="0.6"/><circle cx="320" cy="420" r="1.2" fill="white" opacity="0.8"/><circle cx="480" cy="480" r="0.9" fill="white" opacity="0.7"/><circle cx="620" cy="440" r="0.6" fill="white" opacity="0.5"/><circle cx="780" cy="460" r="1.3" fill="white" opacity="0.9"/><circle cx="880" cy="420" r="0.8" fill="white" opacity="0.7"/><circle cx="120" cy="550" r="0.5" fill="white" opacity="0.5"/><circle cx="270" cy="580" r="1.1" fill="white" opacity="0.8"/><circle cx="420" cy="560" r="0.7" fill="white" opacity="0.6"/><circle cx="570" cy="590" r="1.4" fill="white" opacity="0.9"/><circle cx="720" cy="570" r="0.9" fill="white" opacity="0.7"/><circle cx="870" cy="550" r="0.6" fill="white" opacity="0.6"/></svg>') repeat;
  background-size: 1000px 1000px;
  z-index: 1;
  pointer-events: none;
}

.reviews-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000"><circle cx="150" cy="200" r="0.3" fill="white" opacity="0.15"/><circle cx="350" cy="300" r="0.5" fill="white" opacity="0.2"/><circle cx="550" cy="250" r="0.2" fill="white" opacity="0.1"/><circle cx="750" cy="350" r="0.4" fill="white" opacity="0.18"/><circle cx="250" cy="500" r="0.3" fill="white" opacity="0.12"/><circle cx="650" cy="450" r="0.6" fill="white" opacity="0.22"/></svg>') repeat;
  background-size: 1000px 1000px;
  animation: continuousStarField 45s linear infinite reverse;
  z-index: 1;
}

.reviews-container {
  max-width: 1000px;
  margin: 0 auto;
  padding: 0 var(--spacing-lg);
  position: relative;
  z-index: 2;
}

.reviews-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: var(--spacing-xl);
  margin-top: var(--spacing-xl);
  margin-bottom: var(--spacing-2xl);
}

.review-card {
  background: var(--card-bg);
  border: 1px solid var(--border-color);
  border-radius: 50px;
  padding: var(--spacing-2xl);
  backdrop-filter: blur(20px);
  transition: var(--transition);
  position: relative;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);
  box-shadow: var(--shadow-lg);
}

.review-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  opacity: 1;
  transition: opacity 0.3s ease;
}

.review-card:hover::before {
  opacity: 1;
}

.review-card.featured {
  border: 1px solid var(--border-color-hover);
  background: var(--card-bg-hover);
  transform: scale(1.02);
}

.review-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg, transparent, rgba(0, 0, 0, 0.5), transparent);
  opacity: 0;
  transition: var(--transition);
}

.review-card.featured::before {
  background: linear-gradient(90deg, transparent, rgba(0, 0, 0, 0.8), transparent);
  opacity: 1;
}

.review-card:hover {
  transform: translateY(-5px);
  border-color: var(--border-color-hover);
  background: var(--card-bg-hover);
  box-shadow: var(--shadow-xl);
}

.review-card:hover::before {
  opacity: 1;
}

.review-stars {
  display: flex;
  gap: var(--spacing-xs);
  margin-bottom: var(--spacing-sm);
}

.review-stars i {
  color: var(--accent-color);
  font-size: 1.2rem;
  text-shadow: 0 0 10px rgba(63, 114, 129, 0.5);
  animation: starTwinkle 3s ease-in-out infinite;
  animation-delay: calc(var(--i) * 0.2s);
}

.review-stars i:nth-child(1) { --i: 0; }
.review-stars i:nth-child(2) { --i: 1; }
.review-stars i:nth-child(3) { --i: 2; }
.review-stars i:nth-child(4) { --i: 3; }
.review-stars i:nth-child(5) { --i: 4; }

@keyframes starTwinkle {
  0%, 100% { opacity: 1; transform: scale(1); }
  50% { opacity: 0.7; transform: scale(1.1); }
}

.review-text {
  font-size: 1.1rem;
  color: var(--text-secondary);
  line-height: 1.6;
  margin: 0;
  flex: 1;
  font-style: italic;
  position: relative;
}

.review-text::before {
  content: '"';
  font-size: 3rem;
  color: var(--accent-color);
  opacity: 0.3;
  position: absolute;
  top: -10px;
  left: -10px;
  font-family: serif;
}

.review-author {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  margin-top: auto;
}

.author-avatar {
  width: 50px;
  height: 50px;
  background: linear-gradient(135deg, var(--accent-color), #7289DA);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.2rem;
  flex-shrink: 0;
  box-shadow: 0 0 20px rgba(88, 101, 242, 0.4);
}

.author-info {
  flex: 1;
}

.author-name {
  font-size: 1rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--spacing-xs);
}

.author-role {
  font-size: 0.9rem;
  color: var(--text-secondary);
  opacity: 0.8;
}

/* Reviews Stats */
.reviews-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--spacing-xl);
  margin-top: var(--spacing-2xl);
  padding: var(--spacing-2xl);
  background: rgba(15, 17, 20, 0.6);
  border-radius: var(--radius-lg);
  border: 1px solid rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px);
}

.stat-item {
  text-align: center;
  padding: var(--spacing-lg);
  border-radius: var(--radius-md);
  background: rgba(255, 255, 255, 0.05);
  transition: var(--transition);
  position: relative;
  overflow: hidden;
}

.stat-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(0, 0, 0, 0.1), transparent);
  transition: left 0.6s ease;
}

.stat-item:hover::before {
  left: 100%;
}

.stat-item:hover {
  background: rgba(0, 0, 0, 0.1);
  transform: translateY(-5px);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

.stat-number {
  font-size: 2.5rem;
  font-weight: 800;
  color: #000000;
  margin-bottom: var(--spacing-sm);
  text-shadow: 0 0 20px rgba(0, 0, 0, 0.5);
  animation: numberGlow 3s ease-in-out infinite;
}

@keyframes numberGlow {
  0%, 100% { text-shadow: 0 0 20px rgba(0, 0, 0, 0.5); }
  50% { text-shadow: 0 0 30px rgba(0, 0, 0, 0.8); }
}

.stat-label {
  font-size: 1rem;
  color: var(--text-secondary);
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 1px;
}

/* Builder Section */
.builder-section {
  background: #000000;
  padding-top: 80px;
}

.builder-header {
  background: linear-gradient(135deg, #0a0a0a 0%, #000000 100%);
  padding: var(--spacing-xl) 0;
  text-align: center;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.builder-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: var(--spacing-sm);
}

.builder-description {
  font-size: 1.1rem;
  color: var(--text-secondary);
  max-width: 600px;
  margin: 0 auto;
}

.builder-main {
  padding: var(--spacing-xl) 0;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-lg);
  padding-bottom: var(--spacing-md);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.card-header h3 {
  margin: 0;
  font-size: 1.3rem;
  font-weight: 600;
  color: var(--text-primary);
}

.card-actions {
  display: flex;
  gap: var(--spacing-sm);
}

.btn-icon {
  width: 36px;
  height: 36px;
  background: rgba(255, 255, 255, 0.1);
  border: none;
  border-radius: var(--radius-md);
  color: var(--text-secondary);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: var(--transition);
}

.btn-icon:hover {
  background: rgba(255, 255, 255, 0.2);
  color: var(--text-primary);
}

.preview-actions {
  display: flex;
  gap: var(--spacing-sm);
}

.action-button {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  padding: var(--spacing-sm) var(--spacing-md);
  background: rgba(25, 27, 30, 0.9);
  border: none;
  border-radius: var(--radius-md);
  color: var(--text-primary);
  font-weight: 500;
  cursor: pointer;
  transition: var(--transition);
}

.action-button:hover {
  background: rgba(30, 32, 35, 0.95);
  transform: translateY(-2px);
}

.action-button.primary {
  background: var(--accent-color);
}

.action-button.primary:hover {
  background: var(--accent-hover);
}



/* Responsive Design */
@media (max-width: 1024px) {
  .hero-container {
    padding: 0 var(--spacing-md);
  }

  .hero-title {
    font-size: 3rem;
  }

  .features-grid {
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  }


}

@media (max-width: 768px) {
  .navbar {
    height: 70px;
  }

  .mobile-menu-toggle {
    display: block;
  }

  .nav-links {
    position: fixed;
    top: 70px;
    left: 0;
    right: 0;
    background: var(--card-bg);
    backdrop-filter: blur(25px);
    flex-direction: column;
    padding: var(--spacing-xl);
    gap: var(--spacing-lg);
    transform: translateY(-100%);
    opacity: 0;
    visibility: hidden;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    border-bottom: 1px solid var(--border-color);
    border-radius: 0;
    box-shadow: var(--shadow-xl);
    border: none;
  }

  .nav-links::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg, transparent, var(--accent-color), transparent);
    animation: shimmer 3s ease-in-out infinite;
  }

  .nav-links.mobile-open {
    transform: translateY(0);
    opacity: 1;
    visibility: visible;
  }

  .nav-links .nav-link {
    width: 100%;
    text-align: center;
    padding: var(--spacing-md);
    font-size: 1.1rem;
    border-radius: var(--radius-lg);
    background: var(--input-bg);
    border: 1px solid var(--border-color);
    margin-bottom: var(--spacing-sm);
  }

  .nav-links .nav-link:hover {
    background: var(--input-bg-hover);
    border-color: var(--border-color-hover);
    transform: scale(1.02);
  }

  .nav-links .nav-cta {
    width: 100%;
    text-align: center;
    margin-top: var(--spacing-md);
    padding: var(--spacing-lg);
    font-size: 1.1rem;
  }

  .mobile-menu-toggle {
    display: flex;
    transition: var(--transition);
  }

  .mobile-menu-toggle.active {
    transform: translateY(-50%) rotate(180deg);
    background: var(--card-bg-hover);
    border-color: var(--border-color-hover);
  }

  .hero-title {
    font-size: 2.5rem;
  }

  .hero-content {
    max-width: 100%;
    padding: 0 var(--spacing-md);
  }

  .hero-actions {
    flex-direction: column;
    align-items: stretch;
  }

  .hero-stats {
    justify-content: center;
    gap: var(--spacing-lg);
  }

  .section-title {
    font-size: 1.8rem;
  }

  .features-grid {
    grid-template-columns: 1fr;
  }



  /* Responsive scrolling sections */
  .server-card {
    min-width: 250px;
  }

  .integration-card {
    min-width: 340px;
    max-width: 380px;
    height: 200px;
    padding: var(--spacing-xl);
    gap: var(--spacing-lg);
  }

  .integration-icon {
    width: 60px;
    height: 60px;
    font-size: 2rem;
  }

  .integration-header {
    gap: var(--spacing-lg);
  }

  .integration-name {
    font-size: 1.2rem;
  }

  .integration-description {
    font-size: 0.9rem;
    padding-bottom: var(--spacing-xl);
  }

  .integration-name {
    font-size: 1rem;
  }

  .integration-description {
    font-size: 0.8rem;
  }

  .servers-track {
    animation: scrollRightToLeft 40s linear infinite !important;
  }

  .integrations-track {
    animation: scrollLeftToRight 40s linear infinite !important;
  }

  /* Responsive Voicemaster section */
  .voicemaster-grid {
    grid-template-columns: 1fr;
    grid-template-rows: auto;
    gap: var(--spacing-lg);
  }

  .voicemaster-card {
    flex-direction: column;
    text-align: center;
    padding: var(--spacing-xl);
    gap: var(--spacing-lg);
    min-height: auto !important;
    grid-column: auto !important;
    grid-row: auto !important;
  }

  .voicemaster-visual {
    min-width: auto;
    width: 100%;
  }

  .interface-controls {
    grid-template-columns: 1fr;
  }

  /* Responsive Music section */
  .music-grid {
    grid-template-columns: 1fr;
    grid-template-rows: auto;
    gap: var(--spacing-lg);
  }

  .music-card {
    flex-direction: column;
    text-align: center;
    padding: var(--spacing-xl);
    gap: var(--spacing-lg);
    min-height: auto !important;
    grid-column: auto !important;
    grid-row: auto !important;
  }

  .music-visual {
    min-width: auto;
    width: 100%;
  }

  .filters-demo,
  .queue-demo,
  .spotify-demo {
    min-width: auto;
    max-width: 100%;
  }

  .spotify-controls {
    gap: var(--spacing-md);
  }

  /* Responsive Powerful Features section */
  .powerful-features-grid {
    grid-template-columns: 1fr;
    grid-template-rows: auto;
    gap: var(--spacing-lg);
  }

  .powerful-feature-card {
    padding: var(--spacing-xl);
    gap: var(--spacing-md);
    grid-column: auto !important;
    grid-row: auto !important;
  }

  .powerful-feature-icon {
    width: 60px;
    height: 60px;
    font-size: 2rem;
  }

  .powerful-feature-title {
    font-size: 1.2rem;
  }

  .powerful-feature-description {
    font-size: 0.9rem;
  }

  /* Responsive Reviews section */
  .reviews-grid {
    grid-template-columns: 1fr;
    gap: var(--spacing-lg);
  }

  .review-card {
    padding: var(--spacing-xl);
    gap: var(--spacing-md);
  }

  .review-card.featured {
    transform: none;
  }

  .review-text {
    font-size: 1rem;
  }

  .reviews-stats {
    grid-template-columns: repeat(2, 1fr);
    gap: var(--spacing-lg);
    padding: var(--spacing-xl);
  }

  .stat-number {
    font-size: 2rem;
  }

  .stat-label {
    font-size: 0.9rem;
  }
}


