/* Common Background System for All Pages */

/* Base Background */
body {
  background: linear-gradient(135deg, var(--background) 0%, var(--background-secondary) 50%, var(--background) 100%);
  position: relative;
  min-height: 100vh;
  overflow-x: hidden;
  /* Simple bounce prevention */
  overscroll-behavior: none;
}

/* Mobile-specific background optimizations */
@media (max-width: 768px) {
  body {
    overflow-x: hidden !important;
    width: 100% !important;
    max-width: 100% !important;
    -webkit-text-size-adjust: 100%;
    -ms-text-size-adjust: 100%;
    /* Simple mobile bounce prevention */
    overscroll-behavior: none;
  }
}

/* Background Container */
.page-background {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1;
  pointer-events: none;
}

/* Star Layers */
.stars {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: transparent url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000"><circle cx="156" cy="123" r="1" fill="white" opacity="0.8"/><circle cx="423" cy="267" r="0.7" fill="%237a9fb0" opacity="0.7"/><circle cx="678" cy="189" r="0.8" fill="white" opacity="0.9"/><circle cx="234" cy="456" r="0.6" fill="%237a9fb0" opacity="0.6"/><circle cx="789" cy="345" r="0.9" fill="white" opacity="0.7"/><circle cx="567" cy="678" r="0.7" fill="%237a9fb0" opacity="0.8"/><circle cx="345" cy="789" r="0.8" fill="white" opacity="0.6"/><circle cx="812" cy="234" r="0.6" fill="%237a9fb0" opacity="0.7"/><circle cx="123" cy="567" r="0.7" fill="white" opacity="0.8"/><circle cx="456" cy="123" r="0.6" fill="%237a9fb0" opacity="0.6"/><circle cx="89" cy="834" r="0.8" fill="white" opacity="0.7"/><circle cx="834" cy="567" r="0.6" fill="%237a9fb0" opacity="0.6"/><circle cx="567" cy="89" r="0.9" fill="white" opacity="0.8"/><circle cx="234" cy="712" r="0.7" fill="%237a9fb0" opacity="0.7"/><circle cx="712" cy="456" r="0.8" fill="white" opacity="0.9"/><circle cx="456" cy="234" r="0.6" fill="%237a9fb0" opacity="0.6"/><circle cx="678" cy="812" r="0.7" fill="white" opacity="0.7"/><circle cx="345" cy="123" r="0.6" fill="%237a9fb0" opacity="0.6"/></svg>') repeat;
  background-size: 1000px 1000px;
  z-index: 1;
  pointer-events: none;
  opacity: 0.6;
}

.twinkling {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: transparent url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000"><circle cx="289" cy="178" r="0.4" fill="white" opacity="0.3"/><circle cx="567" cy="345" r="0.3" fill="%237a9fb0" opacity="0.25"/><circle cx="734" cy="289" r="0.5" fill="white" opacity="0.35"/><circle cx="123" cy="456" r="0.2" fill="%237a9fb0" opacity="0.2"/><circle cx="456" cy="678" r="0.4" fill="white" opacity="0.3"/><circle cx="678" cy="123" r="0.3" fill="%237a9fb0" opacity="0.25"/></svg>') repeat;
  background-size: 1000px 1000px;
  animation: continuousStarMovement 25s linear infinite;
  z-index: 1;
  pointer-events: none;
  opacity: 0.5;
}

@keyframes continuousStarMovement {
  0% { background-position: 0 0; }
  100% { background-position: 1000px 1000px; }
}

/* Milky Way Effect */
.milky-way {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(ellipse at center, 
    rgba(122, 159, 176, 0.1) 0%, 
    rgba(122, 159, 176, 0.05) 30%, 
    transparent 70%);
  z-index: 2;
  pointer-events: none;
  opacity: 0.7;
}

/* Content Layer */
.page-content {
  position: relative;
  z-index: 10;
}

/* Ensure all main content is above background */
nav,
main,
section,
footer {
  position: relative;
  z-index: 10;
}
