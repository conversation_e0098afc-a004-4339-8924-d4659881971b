/* Builder Page - Epic Night Sky Theme */

/* Main Builder Page Container */
.builder-page {
  min-height: 100vh;
  position: relative;
  overflow-x: hidden;
}













/* Night Sky Animations */
@keyframes continuousStarMovement {
  0% { background-position: 0 0; }
  100% { background-position: 1000px 1000px; }
}

@keyframes continuousTwinkling {
  0% { background-position: 0 0; }
  100% { background-position: -1000px -1000px; }
}

@keyframes continuousDrift {
  0% { background-position: 0 0; }
  100% { background-position: 1000px 0; }
}

@keyframes float {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  33% { transform: translateY(-20px) rotate(1deg); }
  66% { transform: translateY(-10px) rotate(-1deg); }
}

@keyframes scrollingStars {
  0% { transform: translateY(0px); }
  100% { transform: translateY(-200px); }
}

/* Builder Hero Section */
.builder-hero {
  min-height: 100vh;
  display: flex;
  align-items: center;
  position: relative;
  overflow: hidden;
  z-index: 2;
}

.builder-hero-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--spacing-lg);
  text-align: center;
  position: relative;
  z-index: 3;
}

.builder-badge {
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-xs);
  background: rgba(88, 101, 242, 0.1);
  border: 1px solid rgba(88, 101, 242, 0.3);
  padding: var(--spacing-xs) var(--spacing-md);
  border-radius: 50px;
  color: var(--accent-color);
  font-size: 0.9rem;
  font-weight: 600;
  margin-bottom: var(--spacing-lg);
  animation: pulse 2s ease-in-out infinite;
}

.builder-title {
  font-size: 4rem;
  font-weight: 800;
  margin-bottom: var(--spacing-lg);
  line-height: 1.1;
}

.gradient-text {
  background: linear-gradient(135deg, var(--accent-color), #7289DA);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
}

.builder-description {
  font-size: 1.25rem;
  color: var(--text-secondary);
  max-width: 600px;
  margin: 0 auto var(--spacing-2xl);
  line-height: 1.6;
}

.builder-actions {
  display: flex;
  gap: var(--spacing-md);
  justify-content: center;
  align-items: center;
  flex-wrap: wrap;
  margin-bottom: var(--spacing-2xl);
}

.btn-primary {
  background: linear-gradient(135deg, var(--accent-hover), var(--accent-color));
  color: white;
  padding: var(--spacing-md) var(--spacing-2xl);
  border-radius: 50px;
  font-weight: 700;
  font-size: 1.1rem;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-sm);
  transition: var(--transition);
  border: 1px solid rgba(255, 255, 255, 0.2);
  cursor: pointer;
  box-shadow:
    0 8px 25px rgba(63, 114, 129, 0.4),
    inset 0 1px 0 rgba(255, 255, 255, 0.3),
    inset 0 -1px 0 rgba(0, 0, 0, 0.1);
}

.btn-primary:hover {
  transform: translateY(-3px) scale(1.05);
  box-shadow:
    0 12px 35px rgba(63, 114, 129, 0.6),
    inset 0 1px 0 rgba(255, 255, 255, 0.4),
    inset 0 -1px 0 rgba(0, 0, 0, 0.2);
  border-color: rgba(255, 255, 255, 0.3);
}

.btn-secondary {
  background: var(--card-bg);
  color: var(--text-primary);
  padding: var(--spacing-md) var(--spacing-2xl);
  border-radius: 50px;
  font-weight: 700;
  font-size: 1.1rem;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-sm);
  transition: var(--transition);
  border: 1px solid var(--border-color);
  cursor: pointer;
  backdrop-filter: blur(10px);
  box-shadow:
    0 8px 25px rgba(0, 0, 0, 0.4),
    inset 0 1px 0 rgba(63, 114, 129, 0.3),
    inset 0 -1px 0 rgba(0, 0, 0, 0.3);
}

.btn-secondary:hover {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.25), rgba(255, 255, 255, 0.1));
  border-color: rgba(255, 255, 255, 0.5);
  transform: translateY(-3px) scale(1.05);
  box-shadow:
    0 12px 35px rgba(0, 0, 0, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.5),
    inset 0 -1px 0 rgba(0, 0, 0, 0.2);
}

/* Builder Main Section */
.builder-main {
  padding: 60px 0;
  position: relative;
  z-index: 2;
  width: 100vw;
  margin: 0;
  box-sizing: border-box;
}

/* New Embed Builder Layout */
.embed-builder-container {
  width: 100vw;
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 24px;
  position: relative;
  z-index: 2;
  box-sizing: border-box;
}

.embed-builder-layout {
  display: flex;
  width: 100%;
  height: auto;
  min-height: auto;
  gap: 0;
}

.left-panel {
  width: 50%;
  background-color: #0a0a0a;
  padding: 24px;
  border-radius: 8px 0 0 8px;
  border: 1px solid #1a1a1a;
}

.right-panel {
  width: 50%;
  background-color: #000000;
  padding: 24px;
  display: flex;
  flex-direction: column;
  border-radius: 0 8px 8px 0;
  border: 1px solid #1a1a1a;
  border-left: none;
}

.builder-title {
  font-size: 24px;
  font-weight: 600;
  margin-bottom: 24px;
  color: #ffffff;
}

.section {
  margin-bottom: 24px;
}

.section-title {
  font-size: 14px;
  font-weight: 500;
  color: #b0b0b0;
  margin-bottom: 8px;
}

.color-section-wrapper {
  margin-bottom: 24px;
}

.color-section {
  display: flex;
  align-items: center;
  gap: 12px;
}

.color-swatch {
  width: 40px;
  height: 40px;
  border-radius: 6px;
  border: 2px solid #404040;
  cursor: pointer;
}

.color-text-input {
  flex: 1;
  background-color: #1a1a1a;
  border: 1px solid #404040;
  border-radius: 6px;
  padding: 8px 12px;
  color: #ffffff;
  font-size: 14px;
  font-family: 'Monaco', 'Menlo', monospace;
}

.color-text-input:focus {
  outline: none;
  border-color: #729bb0;
}

.color-text-input::placeholder {
  color: #666666;
}

.form-row {
  display: flex;
  gap: 12px;
  margin-bottom: 16px;
}

.form-group {
  flex: 1;
}

.form-group.full-width {
  width: 100%;
}

.input, .textarea {
  width: 100%;
  background-color: #1a1a1a;
  border: 1px solid #404040;
  border-radius: 6px;
  padding: 12px;
  color: #ffffff;
  font-size: 14px;
  font-family: inherit;
  resize: none;
  box-sizing: border-box;
}

.input:focus, .textarea:focus {
  outline: none;
  border-color: #729bb0;
}

.textarea {
  min-height: 80px;
}

.textarea.large {
  min-height: 120px;
}

.timestamp-wrapper {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  background: rgba(63, 114, 129, 0.05);
  border: 1px solid rgba(63, 114, 129, 0.2);
  border-radius: var(--radius-md);
  padding: var(--spacing-md);
  backdrop-filter: blur(10px);
  transition: var(--transition);
  cursor: pointer;
}

.timestamp-wrapper:hover {
  background: rgba(63, 114, 129, 0.08);
  border-color: rgba(63, 114, 129, 0.3);
}

.custom-checkbox {
  width: 24px;
  height: 24px;
  background: rgba(0, 0, 0, 0.3);
  border: 2px solid rgba(255, 255, 255, 0.2);
  border-radius: var(--radius-sm);
  cursor: pointer;
  position: relative;
  appearance: none;
  -webkit-appearance: none;
  -moz-appearance: none;
  transition: var(--transition);
  flex-shrink: 0;
}

.custom-checkbox:hover {
  border-color: rgba(63, 114, 129, 0.5);
  background: rgba(63, 114, 129, 0.1);
}

.custom-checkbox:checked {
  background: linear-gradient(135deg, var(--accent-color), #7289DA);
  border-color: var(--accent-color);
  box-shadow: 0 2px 8px rgba(63, 114, 129, 0.3);
}

.custom-checkbox:checked::after {
  content: '✓';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: #ffffff;
  font-size: 14px;
  font-weight: bold;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.checkbox-label {
  color: var(--text-primary) !important;
  font-size: 0.95rem !important;
  font-weight: 500 !important;
  margin: 0 !important;
  cursor: pointer !important;
  user-select: none !important;
  transition: var(--transition) !important;
}

.timestamp-wrapper:hover .checkbox-label {
  color: var(--accent-color) !important;
}

.fields-buttons-container {
  margin-top: 32px;
}

.fields-section, .buttons-section-bottom {
  margin-bottom: 24px;
}

.section-with-button {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.section-header {
  font-size: 18px;
  font-weight: 600;
  color: #ffffff;
}

.add-button {
  background-color: #729bb0;
  border: 1px solid #729bb0;
  border-radius: 8px;
  padding: 12px 20px;
  color: #ffffff;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
  transition: all 0.2s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.add-button:hover {
  background-color: #5a8a9f;
  border-color: #5a8a9f;
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.4);
}

/* Add button icon styling moved to after pseudo-element for shimmer effect */

.top-buttons {
  display: flex;
  gap: 12px;
  margin-bottom: 24px;
}

.top-button {
  padding: 12px 20px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
  border: none;
  transition: all 0.2s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.top-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.4);
}

.generate-button {
  background-color: #729bb0;
  color: #ffffff;
}

.generate-button:hover {
  background-color: #5a8a9f;
}

.copy-button, .import-button {
  background-color: #1a1a1a;
  color: #ffffff;
  border: 1px solid #404040;
}

.copy-button:hover, .import-button:hover {
  background-color: #2a2a2a;
  border-color: #555555;
}

.top-button i {
  font-size: 14px;
}

.preview-area {
  flex: 1;
  background-color: #0a0a0a;
  border-radius: 8px;
  border: 1px solid #1a1a1a;
  display: flex;
  flex-direction: column;
  min-height: 500px;
}

/* Dark Theme Glass Cards */
.glass-card {
  background: #1a1a1a;
  border: 1px solid #2a2a2a;
  border-radius: 8px;
  padding: 24px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.4);
  position: relative;
  overflow: hidden;
  width: 100%;
  box-sizing: border-box;
}





@keyframes continuousStarField {
  0% { background-position: 0 0; }
  100% { background-position: 1000px 1000px; }
}

/* Dark Theme Card Headers */
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 0 0 16px 0;
  border-bottom: 1px solid #3f4147;
  position: relative;
}

.card-header::after {
  content: '';
  position: absolute;
  bottom: -1px;
  left: 0;
  width: 80px;
  height: 2px;
  background: linear-gradient(90deg, var(--accent-color), #7289DA);
  border-radius: 1px;
  box-shadow: 0 0 10px rgba(88, 101, 242, 0.5);
}

.card-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #ffffff;
  display: flex;
  align-items: center;
  gap: 8px;
  letter-spacing: 0;
  line-height: 1.4;
}

.card-header h3 i {
  color: var(--accent-color);
  font-size: 1.4rem;
  margin-right: var(--spacing-xs);
  opacity: 0.9;
}

.card-actions {
  display: flex;
  gap: var(--spacing-xs);
  margin-left: var(--spacing-md);
  margin-right: var(--spacing-md);
}

@keyframes pulse {
  0%, 100% { transform: scale(1); opacity: 1; }
  50% { transform: scale(1.1); opacity: 0.8; }
}

/* Enhanced Form Styling */
.glass-card form {
  width: 100%;
  padding: var(--spacing-md);
}

.glass-card .form-group {
  margin-bottom: var(--spacing-xl);
  width: 100%;
}

.glass-card .form-row {
  display: grid;
  grid-template-columns: 1fr;
  gap: var(--spacing-lg);
  margin-bottom: var(--spacing-xl);
}

@media (min-width: 768px) {
  .glass-card .form-row {
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-lg);
  }

  /* Ensure author section stays in one row on desktop */
  .glass-card .form-row:has(#authorName),
  .glass-card .form-row:nth-of-type(2) {
    grid-template-columns: 1fr 1fr 1fr;
    gap: var(--spacing-md);
  }
}

.glass-card input,
.glass-card textarea,
.glass-card select {
  width: 100%;
  padding: var(--spacing-lg);
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.15);
  border-radius: var(--radius-md);
  color: var(--text-primary);
  font-size: 1rem;
  font-family: inherit;
  line-height: 1.5;
  transition: var(--transition);
  box-sizing: border-box;
}

.glass-card textarea {
  min-height: 120px;
  resize: vertical;
}

.glass-card input:focus,
.glass-card textarea:focus,
.glass-card select:focus {
  outline: none;
  border-color: var(--accent-color);
  background: rgba(255, 255, 255, 0.08);
  box-shadow: 0 0 0 2px rgba(88, 101, 242, 0.2);
  transform: translateY(-1px);
}

.glass-card label {
  display: block;
  margin-bottom: var(--spacing-md);
  color: var(--text-primary);
  font-weight: 700;
  font-size: 1rem;
  letter-spacing: 0.5px;
}

/* Form Section Headers */
.form-section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 24px 0 16px 0;
  padding-bottom: 0;
  border-bottom: none;
}

.form-section-header h3 {
  color: #ffffff;
  font-size: 18px;
  font-weight: 600;
  margin: 0;
}

.add-button {
  background: linear-gradient(135deg, var(--accent-color), #7289DA);
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.1);
  padding: var(--spacing-md) var(--spacing-lg);
  border-radius: var(--radius-md);
  font-weight: 600;
  cursor: pointer;
  font-size: 0.9rem;
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-sm);
  transition: var(--transition);
  backdrop-filter: blur(10px);
  box-shadow:
    0 4px 12px rgba(0, 0, 0, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
  position: relative;
  overflow: hidden;
}

.add-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  transition: left 0.5s ease;
}

.add-button:hover::before {
  left: 100%;
}

.add-button:hover {
  background: linear-gradient(135deg, var(--accent-hover), #6c82d4);
  transform: translateY(-2px);
  box-shadow:
    0 6px 20px rgba(63, 114, 129, 0.4),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
  border-color: rgba(255, 255, 255, 0.2);
}

.add-button:active {
  transform: translateY(0);
  box-shadow:
    0 2px 8px rgba(0, 0, 0, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.add-button::after {
  content: '+';
  font-size: 1.2rem;
  font-weight: bold;
  margin-right: var(--spacing-xs);
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

/* Color Section Styling */
.color-section-wrapper {
  margin-bottom: var(--spacing-lg);
}

.color-section {
  display: flex;
  gap: var(--spacing-md);
  align-items: center;
  background: rgba(63, 114, 129, 0.05);
  border: 1px solid rgba(63, 114, 129, 0.2);
  border-radius: var(--radius-md);
  padding: var(--spacing-md);
  backdrop-filter: blur(10px);
  transition: var(--transition);
}

.color-section:hover {
  background: rgba(63, 114, 129, 0.08);
  border-color: rgba(63, 114, 129, 0.3);
}

.color-swatch {
  width: 50px;
  height: 50px;
  border: 2px solid rgba(255, 255, 255, 0.1);
  border-radius: var(--radius-md);
  cursor: pointer;
  background: transparent;
  padding: 0;
  transition: var(--transition);
  -webkit-appearance: none;
  appearance: none;
}

.color-swatch:hover {
  transform: scale(1.05);
  border-color: rgba(255, 255, 255, 0.2);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.color-swatch::-webkit-color-swatch-wrapper {
  padding: 0;
  border-radius: var(--radius-sm);
}

.color-swatch::-webkit-color-swatch {
  border: none;
  border-radius: var(--radius-sm);
}

.color-text-input {
  flex: 1;
  background: rgba(0, 0, 0, 0.3) !important;
  border: 1px solid rgba(255, 255, 255, 0.1) !important;
  color: var(--text-primary) !important;
  border-radius: var(--radius-sm) !important;
  padding: var(--spacing-sm) var(--spacing-md) !important;
  font-family: 'Monaco', 'Consolas', monospace !important;
  font-size: 0.9rem !important;
  transition: var(--transition) !important;
}

.color-text-input:focus {
  border-color: rgba(63, 114, 129, 0.5) !important;
  box-shadow: 0 0 0 2px rgba(63, 114, 129, 0.2) !important;
  background: rgba(0, 0, 0, 0.4) !important;
}

/* Timestamp Group */
.timestamp-group {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  flex-wrap: wrap;
}

.checkbox-label {
  margin: 0 !important;
  cursor: pointer;
  user-select: none;
}

/* Preview Section Styling */
.embed-preview-container {
  flex: 1;
  padding: 16px;
  background: transparent;
  border-radius: 0;
  border: none;
  overflow-y: auto;
}

.embed-preview {
  width: 100%;
}

.discord-message {
  background: #2f3136;
  padding: 16px;
  border-radius: 8px;
  margin-bottom: 16px;
}

/* Override embed styling for simplified structure */
.embed {
  background: #2f3136 !important;
  border-radius: 4px !important;
  padding: 16px !important;
  border-left: 4px solid transparent !important;
  border-top: none !important;
  border-right: none !important;
  border-bottom: none !important;
  margin-top: 8px !important;
  display: block !important;
  position: relative !important;
  max-width: 520px !important;
}

/* Hide the pill element completely */
.embed-pill {
  display: none !important;
}

/* Simplify embed content */
.embed-content {
  background: transparent !important;
  padding: 0 !important;
  border: none !important;
  border-radius: 0 !important;
  display: block !important;
  width: 100% !important;
}

/* Fix thumbnail positioning in simplified structure */
.embed-thumbnail {
  position: absolute !important;
  top: 16px !important;
  right: 16px !important;
  z-index: 1 !important;
  margin: 0 !important;
}

/* No clearfix needed with absolute positioning */

.code-output {
  margin-top: 16px;
  max-height: 200px;
  overflow-y: auto;
}

.code-block {
  background: #1e1e1e;
  color: #ffffff;
  padding: 16px;
  border-radius: 6px;
  font-family: 'Monaco', 'Menlo', 'Courier New', monospace;
  font-size: 12px;
  line-height: 1.4;
  overflow-x: auto;
  white-space: pre-wrap;
  word-wrap: break-word;
  border: 1px solid #404040;
  margin: 0;
}

/* Button Styling Improvements */
.btn-icon {
  width: 45px;
  height: 45px;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.15), rgba(255, 255, 255, 0.05));
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 50px;
  color: var(--text-secondary);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: var(--transition);

  font-size: 1.1rem;
  backdrop-filter: blur(10px);
  box-shadow:
    0 8px 25px rgba(0, 0, 0, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.4),
    inset 0 -1px 0 rgba(0, 0, 0, 0.1);
}

.btn-icon:hover {
  background: linear-gradient(135deg, var(--accent-color), #7289DA);
  border-color: rgba(255, 255, 255, 0.2);
  color: white;
  transform: translateY(-3px) scale(1.05);
  box-shadow:
    0 12px 35px rgba(88, 101, 242, 0.6),
    inset 0 1px 0 rgba(255, 255, 255, 0.4),
    inset 0 -1px 0 rgba(0, 0, 0, 0.2);
}

/* Preview Actions Enhancement */
.preview-actions {
  display: flex;
  gap: var(--spacing-sm);
  flex-wrap: wrap;
}

.preview-actions .action-button {
  flex: 1;
  min-width: 120px;
  font-weight: 600;

}



/* Import Modal Styling */
.modal {
  display: none;
  position: fixed;
  z-index: 1000;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.7);
  backdrop-filter: blur(5px);
}

#importModal .modal-content {
  max-width: 700px;
  margin: 8% auto;
  padding: 0;
  position: relative;
}

#importModal .glass-card {
  background: #0a0a0a;
  border: 1px solid rgba(88, 101, 242, 0.3);
  padding: var(--spacing-3xl);
  text-align: center;
  display: flex;
  flex-direction: column;
  align-items: center;
  backdrop-filter: blur(20px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.6);
}

#importModal h3 {
  margin: var(--spacing-xl) 0 var(--spacing-2xl) 0;
  font-size: 1.8rem;
  font-weight: 600;
  color: var(--text-primary);
  text-align: center;
  padding-bottom: var(--spacing-xl);
  border-bottom: 2px solid rgba(255, 255, 255, 0.15);
  letter-spacing: 0.5px;
}

#importModal textarea {
  width: 100%;
  min-height: 250px;
  font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
  font-size: 1rem;
  background: rgba(0, 0, 0, 0.8);
  border: 2px solid rgba(88, 101, 242, 0.3);
  border-radius: var(--radius-lg);
  padding: var(--spacing-xl);
  color: var(--text-primary);
  resize: vertical;
  margin-bottom: var(--spacing-2xl);
  box-sizing: border-box;
  line-height: 1.6;
  transition: var(--transition);
}

#importModal textarea:focus {
  outline: none;
  border-color: var(--accent-color);
  box-shadow: 0 0 0 3px rgba(88, 101, 242, 0.3);
  background: rgba(0, 0, 0, 0.9);
}

#importModal .action-button {
  background: linear-gradient(135deg, var(--accent-color), #7289DA);
  color: white;
  padding: var(--spacing-md) var(--spacing-lg);
  border-radius: 50px;
  font-weight: 700;
  font-size: 1rem;
  border: none;
  cursor: pointer;
  transition: var(--transition);
  box-shadow: 0 8px 25px rgba(88, 101, 242, 0.4);
  width: auto;
  min-width: 120px;
}

#importModal .action-button:hover {
  transform: translateY(-3px) scale(1.05);
  box-shadow: 0 12px 35px rgba(88, 101, 242, 0.6);
}

.close-modal {
  position: absolute;
  top: var(--spacing-lg);
  right: var(--spacing-xl);
  font-size: 2rem;
  color: var(--text-secondary);
  cursor: pointer;
  transition: var(--transition);
  z-index: 10;
}

.close-modal:hover {
  color: var(--text-primary);
  transform: scale(1.1);
}

/* Hide All Notifications */
.builder-page .notification,
.notification {
  display: none !important;
}

/* Responsive Design for New Builder */
@media (max-width: 1023px) {
  .embed-builder-layout {
    flex-direction: column;
    height: auto;
    min-height: auto;
  }

  .left-panel, .right-panel {
    width: 100%;
    border-radius: 8px;
    margin-bottom: 16px;
  }

  .right-panel {
    margin-bottom: 0;
  }

  .preview-area {
    min-height: 400px;
  }
}

@media (max-width: 768px) {
  .embed-builder-container {
    padding: 0 16px;
  }

  .left-panel, .right-panel {
    padding: 16px;
  }

  .builder-title {
    font-size: 20px;
    margin-bottom: 16px;
  }

  .form-row {
    flex-direction: column;
    gap: 16px;
  }

  .top-buttons {
    flex-wrap: wrap;
    gap: 8px;
  }

  .top-button {
    flex: 1;
    min-width: 80px;
    padding: 8px 12px;
    font-size: 12px;
  }

  .section-with-button {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }

  .add-button {
    align-self: flex-end;
  }
}

/* Remove internal scrolling */
.left-panel, .right-panel {
  overflow: visible;
}

.embed-preview-container {
  overflow: visible;
}

.preview-area {
  overflow: visible;
}

/* Description Text Size Controls */
.description-container {
  position: relative;
}

.text-size-controls {
  position: absolute;
  top: 8px;
  right: 8px;
  display: flex;
  gap: 4px;
  z-index: 10;
}

.size-btn {
  width: 28px;
  height: 28px;
  background: rgba(88, 101, 242, 0.1);
  border: 1px solid rgba(88, 101, 242, 0.3);
  border-radius: 50%;
  color: var(--accent-color);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: var(--transition);
  font-size: 0.8rem;
}

.size-btn:hover {
  background: rgba(88, 101, 242, 0.2);
  border-color: rgba(88, 101, 242, 0.5);
  transform: scale(1.1);
}

/* Footer and Timestamp Row Layout */
.footer-timestamp-row {
  display: grid;
  grid-template-columns: 1fr 1fr auto;
  gap: var(--spacing-lg);
  align-items: end;
}

.footer-input {
  transform: scale(0.9);
  transform-origin: left;
}

.timestamp-group {
  min-width: 120px;
}

.timestamp-checkbox {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  margin-top: 6px;
}

.timestamp-checkbox .checkbox-label {
  font-size: 0.9rem;
  margin: 0;
}

/* Responsive footer layout */
@media (max-width: 768px) {
  .footer-timestamp-row {
    grid-template-columns: 1fr;
    gap: var(--spacing-md);
  }

  .footer-input {
    transform: none;
  }
}

/* Enhanced Add Button Styling */
.add-button {
  background: rgba(20, 22, 25, 0.9);
  border: 1px solid rgba(88, 101, 242, 0.3);
  color: var(--text-primary);
  padding: var(--spacing-md) var(--spacing-lg);
  border-radius: var(--radius-lg);
  font-size: 0.9rem;
  font-weight: 500;
  transition: var(--transition);
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  cursor: pointer;
  backdrop-filter: blur(10px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.add-button:hover {
  background: rgba(25, 27, 30, 0.95);
  border-color: rgba(88, 101, 242, 0.5);
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.4);
}

.add-button:before {
  content: '+';
  font-size: 1.1rem;
  font-weight: bold;
  color: var(--accent-color);
}

/* Sparkle Animation */
@keyframes sparkle {
  0%, 100% { transform: translateY(0px) scale(1); }
  50% { transform: translateY(-5px) scale(1.1); }
}
