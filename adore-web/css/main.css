:root {
  /* Deep Dark Theme with #3F7281 Accent */
  --background: #0a0a0a;
  --background-secondary: #111111;
  --card-bg: rgba(63, 114, 129, 0.08);
  --card-bg-hover: rgba(63, 114, 129, 0.15);
  --input-bg: rgba(63, 114, 129, 0.05);
  --input-bg-hover: rgba(63, 114, 129, 0.12);
  --text-primary: #ffffff;
  --text-secondary: #ffffff;
  --text-muted: #ffffff;
  --border-color: rgba(63, 114, 129, 0.2);
  --border-color-hover: rgba(63, 114, 129, 0.4);
  --accent-color: #3F7281;
  --accent-hover: #2d5a66;
  --accent-light: rgba(63, 114, 129, 0.1);
  --red: #ED4245;
  --green: #3ba55d;
  --spacing-xs: 4px;
  --spacing-sm: 8px;
  --spacing-md: 16px;
  --spacing-lg: 24px;
  --spacing-xl: 32px;
  --spacing-2xl: 48px;
  --radius-sm: 12px;
  --radius-md: 18px;
  --radius-lg: 24px;
  --radius-xl: 30px;
  --shadow-sm: 0 2px 8px rgba(0, 0, 0, 0.4);
  --shadow-md: 0 4px 12px rgba(0, 0, 0, 0.5);
  --shadow-lg: 0 8px 24px rgba(0, 0, 0, 0.6);
  --shadow-xl: 0 20px 40px rgba(0, 0, 0, 0.7);
  --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  --transition-fast: all 0.15s ease;
  --font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
}



* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  overflow-x: hidden;
  scroll-behavior: smooth;
  /* Simple bounce prevention */
  overscroll-behavior: none;
}

body {
  color: var(--text-primary);
  font-family: var(--font-family);
  line-height: 1.6;
  min-height: 100vh;
  overflow-x: hidden;
  scroll-behavior: smooth;
  /* Simple bounce prevention */
  overscroll-behavior: none;
}

.app-container {
  max-width: 1440px;
  margin: 0 auto;
  padding: var(--spacing-md);
}

/* Prevent horizontal overscroll and ensure proper mobile behavior */
* {
  max-width: 100%;
}

/* Prevent elements from extending beyond viewport */
.app-container,
.hero-container,
.commands-container,
.builder-container,
section,
nav,
main {
  max-width: 100%;
  overflow-x: hidden;
}

/* Enhanced typography */
h1, h2, h3, h4, h5, h6 {
  font-weight: 600;
  line-height: 1.2;
  margin: 0;
}

h1 {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: var(--spacing-sm);
  background: linear-gradient(135deg, var(--accent-color), #7289DA);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
  display: inline-block;
}

h2 {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: var(--spacing-md);
  color: var(--text-primary);
}

h3 {
  font-size: 1.2rem;
  font-weight: 600;
  color: var(--text-primary);
}

main {
  display: flex;
  justify-content: center;
}

.builder-container {
  display: grid;
  grid-template-columns: 1fr;
  gap: var(--spacing-lg);
  width: 100%;
}

@media (min-width: 1024px) {
  .builder-container {
    grid-template-columns: 1fr 1fr;
  }
}

.glass-card {
  background: var(--card-bg);
  border: 1px solid var(--border-color);
  border-radius: 60px;
  padding: var(--spacing-lg);
  box-shadow: var(--shadow-sm);
  position: relative;
  backdrop-filter: blur(20px);
  transition: var(--transition);
  overflow: hidden;
}

.glass-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, rgba(88, 101, 242, 0.3), transparent);
  opacity: 0;
  transition: var(--transition);
}

.glass-card:hover {
  background: var(--card-bg-hover);
  border-color: var(--border-color-hover);
  box-shadow: var(--shadow-lg);
  transform: translateY(-2px);
}

.glass-card:hover::before {
  opacity: 1;
}

button {
  cursor: pointer;
  border: none;
  background: none;
  font-family: var(--font-family);
  font-size: 1rem;
  transition: var(--transition);
  color: var(--text-primary);
}

.action-button {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-xs);
  padding: var(--spacing-md) var(--spacing-lg);
  background: var(--input-bg);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  font-weight: 500;
  font-size: 0.95rem;
  transition: var(--transition);
  cursor: pointer;
}



.action-button:hover {
  background: var(--input-bg-hover);
  border-color: var(--border-color-hover);
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.action-button.primary {
  background: linear-gradient(135deg, var(--accent-color), #7289DA);
  border-color: var(--accent-color);
  color: white;
  font-weight: 600;
}

.action-button.primary:hover {
  background: linear-gradient(135deg, var(--accent-hover), #6c82d4);
  transform: translateY(-3px);
  box-shadow: 0 8px 20px rgba(88, 101, 242, 0.4);
}

.icon {
  width: 20px;
  height: 20px;
}

/* Modal styles */
.modal {
  display: none;
  position: fixed;
  z-index: 100;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(10px);
}

.modal-content {
  max-width: 600px;
  margin: 10% auto;
  padding: var(--spacing-2xl);
  border: 1px solid var(--border-color);
  text-align: left;
  position: relative;
  background: var(--card-bg);
  border-radius: var(--radius-lg);
  backdrop-filter: blur(20px);
  box-shadow: var(--shadow-xl);
}

.modal-content h3 {
  margin: 0 0 var(--spacing-xl) 0;
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--text-primary);
  text-align: center;
  padding-bottom: var(--spacing-lg);
  border-bottom: 2px solid rgba(255, 255, 255, 0.1);
}

.modal-content textarea {
  width: 100%;
  min-height: 200px;
  font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
  font-size: 0.9rem;
  background: var(--input-bg);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  padding: var(--spacing-lg);
  color: var(--text-primary);
  resize: vertical;
  margin-bottom: var(--spacing-xl);
  box-sizing: border-box;
  backdrop-filter: blur(10px);
}

.modal-content textarea:focus {
  outline: none;
  border-color: var(--accent-color);
  box-shadow: 0 0 0 2px rgba(88, 101, 242, 0.2);
}

.close-modal {
  position: absolute;
  top: var(--spacing-lg);
  right: var(--spacing-lg);
  font-size: 1.5rem;
  font-weight: bold;
  color: var(--text-secondary);
  cursor: pointer;
  transition: var(--transition);
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background: var(--input-bg);
  backdrop-filter: blur(10px);
}

.close-modal:hover {
  color: var(--text-primary);
  background: rgba(255, 255, 255, 0.2);
  transform: scale(1.1);
}

.action-button {
  padding: var(--spacing-md) var(--spacing-xl);
  border: none;
  border-radius: var(--radius-md);
  font-weight: 600;
  cursor: pointer;
  transition: var(--transition);
  font-size: 1rem;
  display: block;
  margin: 0 auto;
}

.action-button.primary {
  background: linear-gradient(135deg, var(--accent-color), #7289DA);
  color: white;
  box-shadow: 0 4px 15px rgba(88, 101, 242, 0.4);
}

.action-button.primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(88, 101, 242, 0.6);
}

.close-modal {
  position: absolute;
  top: var(--spacing-md);
  right: var(--spacing-md);
  font-size: 1.5rem;
  cursor: pointer;
  color: var(--text-secondary);
  transition: var(--transition);
}

.close-modal:hover {
  color: var(--text-primary);
  transform: scale(1.1);
}

/* Notification styles */
.notification {
  position: fixed;
  top: 20px;
  right: 20px;
  padding: var(--spacing-md) var(--spacing-lg);
  background: var(--card-bg);
  color: var(--text-primary);
  border-radius: 50px;
  box-shadow: var(--shadow-lg);
  z-index: 1000;
  transform: translateX(100%);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border: 1px solid var(--border-color);
  backdrop-filter: blur(20px);
}

.notification.show {
  transform: translateX(0);
}

.notification.success {
  border-left: 4px solid #43b581;
}

.notification.error {
  border-left: 4px solid var(--red);
}

.close-modal {
  float: right;
  font-size: 1.5rem;
  cursor: pointer;
}

#importCode {
  width: 100%;
  height: 150px;
  margin: var(--spacing-md) 0;
  padding: var(--spacing-sm);
  background-color: var(--input-bg);
  color: var(--text-primary);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-sm);
  resize: none;
}

#confirmImport {
  margin-top: var(--spacing-md);
  width: 100%;
}

/* Mobile-specific optimizations */
@media (max-width: 768px) {
  /* Prevent horizontal overscroll and improve mobile experience */
  html {
    overflow-x: hidden !important;
    width: 100% !important;
    max-width: 100% !important;
    -webkit-text-size-adjust: 100%;
    -ms-text-size-adjust: 100%;
  }

  body {
    overflow-x: hidden !important;
    width: 100% !important;
    max-width: 100% !important;
    /* Simple mobile bounce prevention */
    overscroll-behavior: none;
  }

  /* Ensure all containers respect mobile boundaries */
  .app-container,
  .hero-container,
  .commands-container,
  .builder-container,
  section,
  nav,
  main {
    width: 100% !important;
    max-width: 100% !important;
    overflow-x: hidden !important;
    box-sizing: border-box;
  }

  /* Optimize mobile typography */
  h1 {
    font-size: 2rem;
  }

  h2 {
    font-size: 1.3rem;
  }

  h3 {
    font-size: 1.1rem;
  }

  /* Improve mobile button sizing */
  .action-button {
    padding: var(--spacing-sm) var(--spacing-md);
    font-size: 0.9rem;
    min-height: 44px; /* iOS touch target minimum */
  }

  /* Prevent zoom on form inputs */
  input, textarea, select {
    font-size: 16px !important;
    max-width: 100%;
    box-sizing: border-box;
  }
}
