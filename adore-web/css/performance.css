/**
 * Performance Optimizations
 * CSS optimizations to improve website performance and reduce lag
 */

/* Hardware acceleration for smooth animations */
.hero-background,
.cosmic-bot-container,
.servers-track,
.integrations-track {
  will-change: transform;
  transform: translateZ(0);
}

/* Optimize scrolling animations */
.servers-track,
.integrations-track {
  animation-play-state: running !important;
  backface-visibility: hidden;
  perspective: 1000px;
  animation-timing-function: linear !important;
  /* Prevent any external interference with scrolling animations */
  animation-fill-mode: none !important;
}

/* Reduce motion for users who prefer it */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
  
  .servers-track,
  .integrations-track {
    animation: none !important;
  }
  
  .cosmic-rings,
  .feature-orbs,
  .cosmic-particles,
  .energy-waves {
    animation: none !important;
  }
}

/* Optimize images for better performance */
img {
  image-rendering: -webkit-optimize-contrast;
  image-rendering: crisp-edges;
}

/* Optimize text rendering */
body {
  text-rendering: optimizeSpeed;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Optimize scrolling performance */
* {
  scroll-behavior: smooth;
}

@media (prefers-reduced-motion: reduce) {
  * {
    scroll-behavior: auto;
  }
}

/* Optimize background animations for mobile */
@media (max-width: 768px) {
  .hero-background .stars,
  .hero-background .twinkling,
  .hero-background .clouds,
  .hero-background .hero-particles {
    animation-duration: 60s;
    opacity: 0.3;
  }
  
  .cosmic-rings,
  .feature-orbs,
  .cosmic-particles,
  .energy-waves {
    animation-duration: 8s;
  }
}

/* Optimize for low-end devices */
@media (max-width: 480px) {
  .hero-background .stars,
  .hero-background .twinkling {
    display: none;
  }
  
  .cosmic-particles,
  .energy-waves {
    display: none;
  }
}

/* Optimize scrolling containers */
.servers-scroll-container,
.integrations-scroll-container {
  overflow: hidden;
  contain: layout style paint;
}

/* GPU acceleration for smooth transforms */
.navbar,
.hero-visual,
.cosmic-bot-container,
.feature-orbs,
.cosmic-rings {
  transform: translateZ(0);
  backface-visibility: hidden;
}

/* Optimize animations for better performance */
@keyframes optimizedFadeIn {
  from {
    opacity: 0;
    transform: translate3d(0, 30px, 0);
  }
  to {
    opacity: 1;
    transform: translate3d(0, 0, 0);
  }
}

/* Contain layout shifts */
.section-header,
.feature-card,
.review-card {
  contain: layout style;
}

/* Optimize font loading */
@font-face {
  font-family: 'Inter';
  font-display: swap;
}

/* Reduce paint complexity */
.gradient-text {
  will-change: auto;
}

/* Optimize hover effects */
.feature-card:hover,
.review-card:hover,
.nav-link:hover {
  transform: translateZ(0) translateY(-5px);
  backface-visibility: hidden;
}

/* Optimize scrolling on mobile */
@media (max-width: 768px) {
  body {
    -webkit-overflow-scrolling: touch;
    /* Simple bounce prevention */
    overscroll-behavior: none;
  }

  /* Prevent horizontal overscroll on mobile */
  html, body {
    overflow-x: hidden !important;
    width: 100% !important;
    max-width: 100% !important;
    position: relative;
  }

  /* Optimize touch interactions */
  .servers-scroll-container,
  .nav-scroll-container {
    -webkit-overflow-scrolling: touch;
    touch-action: pan-x;
  }

  /* Prevent zoom on input focus */
  input, textarea, select {
    font-size: 16px !important;
    transform-origin: left top;
  }

  /* Optimize mobile performance */
  .servers-track {
    will-change: scroll-position;
    transform: translateZ(0);
  }
}

/* Optimize critical rendering path */
.above-fold {
  contain: layout style paint;
}

/* Reduce repaints */
.animate-on-scroll,
.animate-on-load {
  contain: layout style;
}

/* Optimize for 60fps animations */
@media (min-width: 769px) {
  .cosmic-rings {
    animation-timing-function: linear;
  }

  .servers-track,
  .integrations-track {
    animation-timing-function: linear !important;
    animation-play-state: running !important;
  }
}

/* Memory optimization */
.hero-background::before,
.hero-background::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  will-change: transform;
}

/* Optimize for touch devices */
@media (hover: none) and (pointer: coarse) {
  .feature-card:hover,
  .review-card:hover {
    transform: none;
  }
}

/* Optimize visibility changes */
.hidden {
  visibility: hidden;
  opacity: 0;
  pointer-events: none;
}

.visible {
  visibility: visible;
  opacity: 1;
  pointer-events: auto;
}

/* Optimize for high refresh rate displays */
@media (min-resolution: 120dpi) {
  .cosmic-rings {
    animation-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  }

  /* Keep scrolling tracks linear for consistent speed */
  .servers-track,
  .integrations-track {
    animation-timing-function: linear !important;
  }
}

/* Optimize layout stability */
.layout-stable {
  contain: layout;
}

/* Optimize for battery saving */
@media (prefers-reduced-motion: reduce) {
  .hero-background,
  .cosmic-bot-container {
    animation: none !important;
  }
}

/* Critical CSS optimizations */
.critical {
  contain: strict;
  content-visibility: auto;
  contain-intrinsic-size: 300px;
}
