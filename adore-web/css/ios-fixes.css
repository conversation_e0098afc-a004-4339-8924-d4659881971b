/**
 * iOS Safari Specific Fixes
 * Addresses rendering issues specific to iOS Safari
 */

/* iOS Safari Detection and Fixes */
@supports (-webkit-touch-callout: none) {
  /* Ensure proper background rendering on iOS */
  body {
    background: #0a0a0a !important;
    background-attachment: fixed;
    -webkit-background-size: cover;
    background-size: cover;
  }
  
  /* Force background gradient to render properly */
  body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #0a0a0a 0%, #111111 50%, #0a0a0a 100%);
    z-index: -1;
    pointer-events: none;
  }
  
  /* Ensure page background container works on iOS */
  .page-background {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    bottom: 0 !important;
    width: 100% !important;
    height: 100% !important;
    z-index: 1 !important;
    pointer-events: none !important;
  }
  
  /* Fix star backgrounds for iOS */
  .stars {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    width: 100% !important;
    height: 100% !important;
    background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000"><circle cx="156" cy="123" r="1" fill="white" opacity="0.8"/><circle cx="423" cy="267" r="0.7" fill="%23ffffff" opacity="0.7"/><circle cx="678" cy="189" r="0.8" fill="white" opacity="0.9"/><circle cx="234" cy="456" r="0.6" fill="%23ffffff" opacity="0.6"/><circle cx="789" cy="345" r="0.9" fill="white" opacity="0.7"/></svg>') !important;
    background-repeat: repeat !important;
    background-size: 1000px 1000px !important;
    z-index: 2 !important;
    pointer-events: none !important;
    opacity: 0.6 !important;
  }
  
  /* Fix twinkling stars for iOS */
  .twinkling {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    width: 100% !important;
    height: 100% !important;
    background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000"><circle cx="289" cy="178" r="0.4" fill="white" opacity="0.3"/><circle cx="567" cy="345" r="0.3" fill="%23ffffff" opacity="0.25"/><circle cx="734" cy="289" r="0.5" fill="white" opacity="0.35"/></svg>') !important;
    background-repeat: repeat !important;
    background-size: 1000px 1000px !important;
    animation: iosStarMovement 25s linear infinite !important;
    z-index: 3 !important;
    pointer-events: none !important;
    opacity: 0.5 !important;
  }
  
  /* Fix milky way gradient for iOS */
  .milky-way {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    width: 100% !important;
    height: 100% !important;
    background: radial-gradient(ellipse at center, 
      rgba(122, 159, 176, 0.1) 0%, 
      rgba(122, 159, 176, 0.05) 30%, 
      transparent 70%) !important;
    z-index: 4 !important;
    pointer-events: none !important;
    opacity: 0.7 !important;
  }
  
  /* Ensure content is above background */
  nav,
  main,
  section,
  footer,
  .hero-section,
  .commands-list,
  .builder-container {
    position: relative !important;
    z-index: 10 !important;
    background: transparent !important;
  }
}

/* iOS specific animation */
@keyframes iosStarMovement {
  0% { 
    background-position: 0 0; 
    transform: translateZ(0);
  }
  100% { 
    background-position: 1000px 1000px; 
    transform: translateZ(0);
  }
}

/* iOS Safari viewport fixes */
@media screen and (-webkit-min-device-pixel-ratio: 1) {
  html {
    height: 100vh !important;
    height: -webkit-fill-available !important;
  }
  
  body {
    min-height: 100vh !important;
    min-height: -webkit-fill-available !important;
  }
  
  /* Fix for iOS Safari address bar */
  .hero-section {
    min-height: 100vh !important;
    min-height: -webkit-fill-available !important;
  }
}

/* iOS specific backdrop-filter fixes */
@supports (-webkit-backdrop-filter: blur(10px)) {
  .glass-card,
  .navbar,
  .modal-content {
    -webkit-backdrop-filter: blur(20px) !important;
    backdrop-filter: blur(20px) !important;
  }
}

/* iOS font rendering fixes */
@supports (-webkit-touch-callout: none) {
  * {
    -webkit-font-smoothing: antialiased !important;
    -moz-osx-font-smoothing: grayscale !important;
  }
  
  /* Fix text rendering on iOS */
  h1, h2, h3, h4, h5, h6, p, span, div {
    -webkit-font-smoothing: antialiased !important;
    -moz-osx-font-smoothing: grayscale !important;
  }
  
  /* Fix gradient text on iOS */
  h1 {
    background: linear-gradient(135deg, #3F7281, #7289DA) !important;
    -webkit-background-clip: text !important;
    -webkit-text-fill-color: transparent !important;
    background-clip: text !important;
  }
}

/* iOS specific CSS variable fallbacks */
@supports (-webkit-touch-callout: none) {
  :root {
    --background: #0a0a0a !important;
    --background-secondary: #111111 !important;
    --text-primary: #ffffff !important;
    --text-secondary: #ffffff !important;
    --card-bg: rgba(63, 114, 129, 0.08) !important;
    --border-color: rgba(63, 114, 129, 0.2) !important;
    --accent-color: #3F7281 !important;
  }
  
  /* Fallback styles if CSS variables fail */
  body {
    color: #ffffff !important;
    background-color: #0a0a0a !important;
  }
  
  .glass-card {
    background: rgba(63, 114, 129, 0.08) !important;
    border: 1px solid rgba(63, 114, 129, 0.2) !important;
  }
  
  .navbar {
    background: rgba(63, 114, 129, 0.08) !important;
    border: 1px solid rgba(63, 114, 129, 0.2) !important;
  }
}

/* iOS performance optimizations */
@supports (-webkit-touch-callout: none) {
  * {
    -webkit-transform: translateZ(0) !important;
    transform: translateZ(0) !important;
    -webkit-backface-visibility: hidden !important;
    backface-visibility: hidden !important;
  }
  
  .page-background,
  .stars,
  .twinkling,
  .milky-way {
    will-change: transform !important;
    -webkit-transform: translateZ(0) !important;
    transform: translateZ(0) !important;
  }
}

/* iOS specific media query fixes */
@media only screen and (max-device-width: 812px) and (-webkit-min-device-pixel-ratio: 2) {
  /* iPhone specific fixes */
  body {
    -webkit-text-size-adjust: 100% !important;
    text-size-adjust: 100% !important;
  }
  
  .page-background {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    width: 100vw !important;
    height: 100vh !important;
    height: -webkit-fill-available !important;
  }
}

/* iPad specific fixes */
@media only screen and (min-device-width: 768px) and (max-device-width: 1024px) and (-webkit-min-device-pixel-ratio: 1) {
  .page-background {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    width: 100vw !important;
    height: 100vh !important;
  }
}

/* Additional iOS background fixes */
.ios-device body {
  background: #0a0a0a !important;
  background-color: #0a0a0a !important;
}

.ios-device body::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #0a0a0a 0%, #111111 50%, #0a0a0a 100%);
  z-index: -1;
  pointer-events: none;
}

/* Force background visibility on iOS */
@supports (-webkit-touch-callout: none) {
  html {
    background: #0a0a0a !important;
    background-color: #0a0a0a !important;
  }

  body {
    background: #0a0a0a !important;
    background-color: #0a0a0a !important;
    background-image: linear-gradient(135deg, #0a0a0a 0%, #111111 50%, #0a0a0a 100%) !important;
  }

  /* Ensure page background is visible */
  .page-background {
    background: #0a0a0a !important;
    background-color: #0a0a0a !important;
  }
}
